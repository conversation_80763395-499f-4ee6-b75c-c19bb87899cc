#!/usr/bin/env python3
"""
测试小星指向大星的准确性
验证每颗小星是否有一个顶点准确指向大星中心
"""

import math
from config import FlagConfig
from geometry import GeometryCalculator

def test_star_pointing():
    """测试小星指向大星的准确性"""
    print("=" * 50)
    print("测试小星指向大星的准确性")
    print("=" * 50)
    
    # 创建配置
    config = FlagConfig()
    calc = GeometryCalculator()
    
    # 获取大星和小星配置
    large_star = config.create_large_star()
    small_stars = config.create_small_stars()
    
    print(f"大星中心位置: ({large_star.center[0]:.1f}, {large_star.center[1]:.1f})")
    print(f"大星半径: {large_star.radius:.1f}")
    print()
    
    # 测试每颗小星
    for i, small_star in enumerate(small_stars, 1):
        print(f"第 {i} 颗小星:")
        print(f"  中心位置: ({small_star.center[0]:.1f}, {small_star.center[1]:.1f})")
        print(f"  半径: {small_star.radius:.1f}")
        print(f"  旋转角度: {small_star.rotation:.3f} 弧度 ({small_star.rotation * 180 / math.pi:.1f}°)")
        
        # 计算小星的顶点
        points = calc.calculate_star_points(
            small_star.center, 
            small_star.radius, 
            small_star.rotation
        )
        
        # 检查哪个顶点最接近指向大星中心
        min_distance = float('inf')
        closest_point_index = -1
        
        # 只检查外顶点（索引为偶数的点）
        for j in range(0, len(points), 2):
            point = points[j]
            
            # 计算从这个顶点到大星中心的向量
            dx = large_star.center[0] - point[0]
            dy = large_star.center[1] - point[1]
            
            # 计算从小星中心到这个顶点的向量
            star_dx = point[0] - small_star.center[0]
            star_dy = point[1] - small_star.center[1]
            
            # 计算从小星中心到大星中心的向量
            target_dx = large_star.center[0] - small_star.center[0]
            target_dy = large_star.center[1] - small_star.center[1]
            
            # 计算两个向量的夹角（用点积）
            dot_product = star_dx * target_dx + star_dy * target_dy
            star_magnitude = math.sqrt(star_dx**2 + star_dy**2)
            target_magnitude = math.sqrt(target_dx**2 + target_dy**2)
            
            if star_magnitude > 0 and target_magnitude > 0:
                cos_angle = dot_product / (star_magnitude * target_magnitude)
                # 限制cos_angle在[-1, 1]范围内，避免浮点误差
                cos_angle = max(-1, min(1, cos_angle))
                angle_diff = math.acos(cos_angle)
                
                if angle_diff < min_distance:
                    min_distance = angle_diff
                    closest_point_index = j // 2  # 外顶点编号
        
        print(f"  最接近指向大星的顶点: 第 {closest_point_index + 1} 个外顶点")
        print(f"  角度偏差: {min_distance * 180 / math.pi:.2f}°")
        
        # 判断是否足够准确（允许1度的误差）
        if min_distance * 180 / math.pi <= 1.0:
            print(f"  ✓ 指向准确")
        else:
            print(f"  ⚠ 指向偏差较大")
        
        print()

def test_angle_calculation():
    """测试角度计算的正确性"""
    print("=" * 50)
    print("测试角度计算的正确性")
    print("=" * 50)
    
    calc = GeometryCalculator()
    
    # 测试几个已知的角度
    test_cases = [
        ((0, 0), (0, 1), 0),        # 正上方，应该是0弧度
        ((0, 0), (1, 0), math.pi/2), # 正右方，应该是π/2弧度
        ((0, 0), (0, -1), math.pi),  # 正下方，应该是π弧度
        ((0, 0), (-1, 0), -math.pi/2), # 正左方，应该是-π/2弧度
    ]
    
    for from_point, to_point, expected_angle in test_cases:
        calculated_angle = calc.calculate_pointing_angle(from_point, to_point)
        angle_diff = abs(calculated_angle - expected_angle)
        
        print(f"从 {from_point} 到 {to_point}:")
        print(f"  期望角度: {expected_angle:.3f} 弧度 ({expected_angle * 180 / math.pi:.1f}°)")
        print(f"  计算角度: {calculated_angle:.3f} 弧度 ({calculated_angle * 180 / math.pi:.1f}°)")
        print(f"  误差: {angle_diff:.3f} 弧度 ({angle_diff * 180 / math.pi:.1f}°)")
        
        if angle_diff < 0.01:  # 允许0.01弧度的误差
            print(f"  ✓ 计算正确")
        else:
            print(f"  ✗ 计算有误")
        print()

if __name__ == "__main__":
    test_angle_calculation()
    test_star_pointing()
