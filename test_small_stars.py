"""
小五角星绘制功能单元测试
验证四颗小星的位置、尺寸、旋转角度和绘制准确性
"""

import unittest
import math
from unittest.mock import Mock

from config import FlagConfig, Star
from star_drawer import StarDrawer
from geometry import GeometryCalculator
from constants import (
    SMALL_STARS_POSITIONS, SMALL_STAR_RADIUS_RATIO, STAR_YELLOW,
    LARGE_STAR_CENTER_X_RATIO, LARGE_STAR_CENTER_Y_RATIO
)


class TestSmallStarsDrawing(unittest.TestCase):
    """小五角星绘制测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = FlagConfig(width=600)  # 标准尺寸
        self.mock_pen = Mock()
        self.star_drawer = StarDrawer(self.mock_pen)
        self.geometry_calc = GeometryCalculator()
    
    def test_small_stars_count(self):
        """测试小星数量"""
        small_stars = self.config.create_small_stars()
        
        self.assertEqual(len(small_stars), 4, msg="必须有4颗小星")
        self.assertEqual(len(SMALL_STARS_POSITIONS), 4, msg="常量定义必须有4个小星位置")
        
        print("✓ 小星数量验证通过: 4颗")
    
    def test_small_stars_positions(self):
        """测试小星位置计算的准确性"""
        small_stars = self.config.create_small_stars()
        
        for i, (star, expected_pos) in enumerate(zip(small_stars, SMALL_STARS_POSITIONS)):
            # 计算期望的绝对坐标
            expected_abs_pos = self.config.get_absolute_position(expected_pos[0], expected_pos[1])
            
            # 验证位置
            self.assertAlmostEqual(star.center[0], expected_abs_pos[0], places=1,
                                  msg=f"第{i+1}颗小星X坐标不正确")
            self.assertAlmostEqual(star.center[1], expected_abs_pos[1], places=1,
                                  msg=f"第{i+1}颗小星Y坐标不正确")
            
            print(f"✓ 第{i+1}颗小星位置验证通过: ({star.center[0]:.1f}, {star.center[1]:.1f})")
    
    def test_small_stars_size(self):
        """测试小星尺寸计算的准确性"""
        small_stars = self.config.create_small_stars()
        expected_radius = self.config.height * SMALL_STAR_RADIUS_RATIO
        
        for i, star in enumerate(small_stars):
            self.assertAlmostEqual(star.radius, expected_radius, places=1,
                                  msg=f"第{i+1}颗小星半径不符合标准")
        
        print(f"✓ 小星尺寸验证通过: 半径 {expected_radius:.1f} 像素")
    
    def test_small_stars_pointing_to_large_star(self):
        """测试小星指向大星中心的角度计算"""
        small_stars = self.config.create_small_stars()
        large_star = self.config.create_large_star()
        
        for i, small_star in enumerate(small_stars):
            # 计算小星到大星的理论角度
            dx = large_star.center[0] - small_star.center[0]
            dy = large_star.center[1] - small_star.center[1]
            expected_angle = math.atan2(dy, dx) - math.pi/2
            
            # 标准化角度到 [0, 2π) 范围
            while expected_angle < 0:
                expected_angle += 2 * math.pi
            while small_star.rotation < 0:
                small_star.rotation += 2 * math.pi
            
            # 验证角度（允许小的误差）
            angle_diff = abs(small_star.rotation - expected_angle)
            if angle_diff > math.pi:
                angle_diff = 2 * math.pi - angle_diff
            
            self.assertLess(angle_diff, 0.01, 
                           msg=f"第{i+1}颗小星旋转角度不正确，应指向大星中心")
            
            print(f"✓ 第{i+1}颗小星指向角度验证通过: {small_star.rotation:.3f} 弧度")
    
    def test_small_stars_color(self):
        """测试小星颜色设置"""
        small_stars = self.config.create_small_stars()
        
        for i, star in enumerate(small_stars):
            self.assertEqual(star.color, STAR_YELLOW,
                           msg=f"第{i+1}颗小星颜色应该为标准金黄色")
        
        print(f"✓ 小星颜色验证通过: {STAR_YELLOW}")
    
    def test_small_stars_geometry_validation(self):
        """测试小星几何形状的准确性"""
        small_stars = self.config.create_small_stars()
        
        for i, star in enumerate(small_stars):
            # 计算五角星顶点
            points = self.geometry_calc.calculate_star_points(
                star.center, star.radius, star.rotation
            )
            
            # 验证几何形状
            is_valid = self.geometry_calc.validate_star_geometry(
                star.center, star.radius, points
            )
            
            self.assertTrue(is_valid, msg=f"第{i+1}颗小星几何形状验证失败")
            self.assertEqual(len(points), 10, msg=f"第{i+1}颗小星应该有10个顶点")
        
        print("✓ 所有小星几何形状验证通过")
    
    def test_small_stars_drawing_sequence(self):
        """测试小星绘制顺序"""
        small_stars = self.config.create_small_stars()
        
        # 验证小星按正确顺序排列（从上到下，从左到右的弧形排列）
        positions = [star.center for star in small_stars]
        
        # 第一颗星应该在最上方
        self.assertEqual(max(pos[1] for pos in positions), positions[0][1],
                        msg="第一颗小星应该在最上方")
        
        # 验证弧形排列的相对位置关系
        self.assertGreater(positions[0][1], positions[1][1], msg="第1颗星应该比第2颗星更高")
        self.assertGreater(positions[1][1], positions[2][1], msg="第2颗星应该比第3颗星更高")
        self.assertGreater(positions[2][1], positions[3][1], msg="第3颗星应该比第4颗星更高")
        
        print("✓ 小星排列顺序验证通过")
    
    def test_small_stars_relative_positions(self):
        """测试小星相对位置的精确性"""
        # 测试不同尺寸下的相对位置
        test_sizes = [300, 600, 900, 1200]
        
        for width in test_sizes:
            config = FlagConfig(width=width)
            small_stars = config.create_small_stars()
            
            for i, (star, expected_pos) in enumerate(zip(small_stars, SMALL_STARS_POSITIONS)):
                # 计算相对位置比例
                flag_top_left = config.flag_top_left
                relative_x = (star.center[0] - flag_top_left[0]) / config.width
                relative_y = (flag_top_left[1] - star.center[1]) / config.height
                
                # 验证比例符合标准
                self.assertAlmostEqual(relative_x, expected_pos[0], places=3,
                                      msg=f"宽度{width}时第{i+1}颗小星X比例不正确")
                self.assertAlmostEqual(relative_y, expected_pos[1], places=3,
                                      msg=f"宽度{width}时第{i+1}颗小星Y比例不正确")
        
        print("✓ 小星相对位置验证通过（多种尺寸）")
    
    def test_small_stars_boundary_validation(self):
        """测试小星边界验证"""
        small_stars = self.config.create_small_stars()
        
        # 定义画布边界
        canvas_bounds = (
            -self.config.width/2 - 50,
            -self.config.height/2 - 50,
            self.config.width/2 + 50,
            self.config.height/2 + 50
        )
        
        for i, star in enumerate(small_stars):
            is_within_bounds = self.star_drawer.validate_star_position(
                star.center, star.radius, canvas_bounds
            )
            self.assertTrue(is_within_bounds, msg=f"第{i+1}颗小星应该在画布范围内")
        
        print("✓ 小星边界验证通过")


class TestSmallStarsIntegration(unittest.TestCase):
    """小五角星集成测试类"""
    
    def test_small_stars_with_large_star_relationship(self):
        """测试小星与大星的关系"""
        config = FlagConfig(width=600)
        large_star = config.create_large_star()
        small_stars = config.create_small_stars()
        
        for i, small_star in enumerate(small_stars):
            # 验证小星在大星右侧（弧形排列）
            if i in [1, 2]:  # 第2、3颗星应该在大星右侧
                self.assertGreater(small_star.center[0], large_star.center[0],
                                 msg=f"第{i+1}颗小星应该在大星右侧")
            
            # 验证小星半径小于大星半径
            self.assertLess(small_star.radius, large_star.radius,
                           msg=f"第{i+1}颗小星半径应该小于大星")
            
            # 验证小星与大星的距离合理
            distance = math.sqrt(
                (small_star.center[0] - large_star.center[0])**2 +
                (small_star.center[1] - large_star.center[1])**2
            )
            self.assertGreater(distance, large_star.radius + small_star.radius,
                             msg=f"第{i+1}颗小星不应与大星重叠")
        
        print("✓ 小星与大星关系验证通过")
    
    def test_small_stars_arc_arrangement(self):
        """测试小星弧形排列"""
        config = FlagConfig(width=600)
        large_star = config.create_large_star()
        small_stars = config.create_small_stars()
        
        # 计算每颗小星到大星的距离和角度
        distances = []
        angles = []
        
        for star in small_stars:
            dx = star.center[0] - large_star.center[0]
            dy = star.center[1] - large_star.center[1]
            distance = math.sqrt(dx*dx + dy*dy)
            angle = math.atan2(dy, dx)
            
            distances.append(distance)
            angles.append(angle)
        
        # 验证弧形排列的特征
        # 1. 角度应该是递减的（从上到下的弧形）
        for i in range(len(angles) - 1):
            self.assertGreater(angles[i], angles[i+1],
                             msg=f"小星{i+1}到{i+2}的角度应该递减（弧形排列）")
        
        print("✓ 小星弧形排列验证通过")
    
    def test_small_stars_standard_compliance(self):
        """测试小星符合国家标准的完整性"""
        config = FlagConfig(width=600)
        small_stars = config.create_small_stars()
        large_star = config.create_large_star()
        
        for i, star in enumerate(small_stars):
            # 综合验证所有标准要求
            expected_pos = SMALL_STARS_POSITIONS[i]
            expected_abs_pos = config.get_absolute_position(expected_pos[0], expected_pos[1])
            
            checks = {
                "位置X": abs(star.center[0] - expected_abs_pos[0]) < 1.0,
                "位置Y": abs(star.center[1] - expected_abs_pos[1]) < 1.0,
                "半径比例": abs(star.radius / config.height - SMALL_STAR_RADIUS_RATIO) < 0.001,
                "颜色标准": star.color == STAR_YELLOW,
                "指向大星": self._verify_pointing_to_large_star(star, large_star)
            }
            
            for check_name, result in checks.items():
                self.assertTrue(result, msg=f"第{i+1}颗小星标准检查失败: {check_name}")
        
        print("✓ 所有小星国家标准符合性验证通过")
    
    def _verify_pointing_to_large_star(self, small_star: Star, large_star: Star) -> bool:
        """验证小星是否指向大星中心"""
        dx = large_star.center[0] - small_star.center[0]
        dy = large_star.center[1] - small_star.center[1]
        expected_angle = math.atan2(dy, dx) - math.pi/2
        
        # 标准化角度
        while expected_angle < 0:
            expected_angle += 2 * math.pi
        while small_star.rotation < 0:
            small_star.rotation += 2 * math.pi
        
        angle_diff = abs(small_star.rotation - expected_angle)
        if angle_diff > math.pi:
            angle_diff = 2 * math.pi - angle_diff
        
        return angle_diff < 0.01


if __name__ == "__main__":
    # 运行测试
    print("=" * 50)
    print("小五角星绘制功能单元测试")
    print("=" * 50)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestSmallStarsDrawing))
    suite.addTests(loader.loadTestsFromTestCase(TestSmallStarsIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✓ 所有测试通过！小五角星绘制功能符合要求")
    else:
        print("✗ 部分测试失败，请检查实现")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"原因: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"原因: {error[1]}")
    print("=" * 50)