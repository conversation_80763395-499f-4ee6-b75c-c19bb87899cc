"""
五角星绘制器模块
实现通用的五角星绘制功能，支持指定中心点、半径和旋转角度

基于国家标准 GB 12982-2004《国旗》的精确绘制要求
"""

import turtle
import math
from typing import Tuple, List, Optional

from geometry import GeometryCalculator
from config import Star
from constants import STAR_YELLOW


class StarDrawer:
    """五角星绘制器类
    
    提供通用的五角星绘制功能，支持：
    - 指定中心点、半径和旋转角度
    - 使用正确的金黄色填充
    - 确保星星形状的准确性
    """
    
    def __init__(self, pen: turtle.Turtle):
        """初始化星星绘制器
        
        Args:
            pen: turtle画笔对象
        """
        self.pen = pen
        self.geometry_calc = GeometryCalculator()
    
    def draw_five_pointed_star(self, 
                             center: Tuple[float, float], 
                             radius: float, 
                             rotation: float = 0.0,
                             color: str = STAR_YELLOW,
                             outline_color: Optional[str] = None,
                             outline_width: int = 1) -> bool:
        """绘制五角星
        
        Args:
            center: 五角星中心坐标 (x, y)
            radius: 外接圆半径
            rotation: 旋转角度（弧度），0表示顶点指向正上方
            color: 填充颜色，默认为金黄色
            outline_color: 边框颜色，None表示与填充色相同
            outline_width: 边框宽度
            
        Returns:
            True表示绘制成功，False表示绘制失败
            
        Raises:
            ValueError: 当参数无效时
            turtle.Terminator: 当turtle窗口被关闭时
        """
        try:
            # 参数验证
            if radius <= 0:
                raise ValueError("半径必须大于0")
            
            if outline_width < 0:
                raise ValueError("边框宽度不能为负数")
            
            # 计算五角星顶点
            points = self.geometry_calc.calculate_star_points(center, radius, rotation)
            
            # 验证几何形状
            if not self.geometry_calc.validate_star_geometry(center, radius, points):
                raise ValueError("五角星几何形状验证失败")
            
            # 设置绘制参数
            self._setup_star_style(color, outline_color or color, outline_width)
            
            # 移动到起始位置（第一个顶点）
            start_point = points[0]
            self.pen.penup()
            self.pen.goto(start_point[0], start_point[1])
            self.pen.pendown()
            
            # 开始填充
            self.pen.begin_fill()
            
            # 绘制五角星轮廓
            self._draw_star_outline(points)
            
            # 结束填充
            self.pen.end_fill()
            
            return True
            
        except turtle.Terminator:
            print("✗ Turtle窗口被关闭，星星绘制中断")
            raise
        except Exception as e:
            print(f"✗ 五角星绘制失败: {e}")
            return False
    
    def draw_star_from_config(self, star: Star) -> bool:
        """根据Star配置对象绘制五角星
        
        Args:
            star: Star配置对象
            
        Returns:
            True表示绘制成功，False表示绘制失败
        """
        return self.draw_five_pointed_star(
            center=star.center,
            radius=star.radius,
            rotation=star.rotation,
            color=star.color,
            outline_color=star.outline_color,
            outline_width=star.outline_width
        )
    
    def _setup_star_style(self, fill_color: str, outline_color: str, outline_width: int):
        """设置星星绘制样式
        
        Args:
            fill_color: 填充颜色
            outline_color: 边框颜色
            outline_width: 边框宽度
        """
        # 设置填充颜色
        self.pen.fillcolor(fill_color)
        
        # 设置边框颜色和宽度
        self.pen.color(outline_color)
        self.pen.pensize(outline_width)
    
    def _draw_star_outline(self, points: List[Tuple[float, float]]):
        """绘制五角星轮廓
        
        Args:
            points: 五角星顶点列表（10个点）
        """
        # 从第一个点开始，按顺序连接所有点
        for i in range(1, len(points)):
            point = points[i]
            self.pen.goto(point[0], point[1])
        
        # 回到起始点闭合图形
        start_point = points[0]
        self.pen.goto(start_point[0], start_point[1])
    
    def move_to_position(self, position: Tuple[float, float]):
        """移动画笔到指定位置（不绘制）
        
        Args:
            position: 目标位置 (x, y)
        """
        self.pen.penup()
        self.pen.goto(position[0], position[1])
        self.pen.pendown()
    
    def get_star_bounding_box(self, center: Tuple[float, float], radius: float) -> Tuple[float, float, float, float]:
        """获取五角星的边界框
        
        Args:
            center: 星星中心坐标
            radius: 外接圆半径
            
        Returns:
            边界框 (min_x, min_y, max_x, max_y)
        """
        points = self.geometry_calc.calculate_star_points(center, radius)
        return self.geometry_calc.get_star_bounding_box(points)
    
    def validate_star_position(self, center: Tuple[float, float], radius: float, 
                             canvas_bounds: Tuple[float, float, float, float]) -> bool:
        """验证星星是否在画布范围内
        
        Args:
            center: 星星中心坐标
            radius: 外接圆半径
            canvas_bounds: 画布边界 (min_x, min_y, max_x, max_y)
            
        Returns:
            True表示星星完全在画布内，False表示超出边界
        """
        star_bounds = self.get_star_bounding_box(center, radius)
        canvas_min_x, canvas_min_y, canvas_max_x, canvas_max_y = canvas_bounds
        star_min_x, star_min_y, star_max_x, star_max_y = star_bounds
        
        return (star_min_x >= canvas_min_x and star_min_y >= canvas_min_y and
                star_max_x <= canvas_max_x and star_max_y <= canvas_max_y)


# 便利函数
def draw_star_at_position(pen: turtle.Turtle, 
                         center: Tuple[float, float], 
                         radius: float, 
                         rotation: float = 0.0,
                         color: str = STAR_YELLOW) -> bool:
    """在指定位置绘制五角星的便利函数
    
    Args:
        pen: turtle画笔对象
        center: 中心坐标
        radius: 半径
        rotation: 旋转角度（弧度）
        color: 颜色
        
    Returns:
        绘制是否成功
    """
    drawer = StarDrawer(pen)
    return drawer.draw_five_pointed_star(center, radius, rotation, color)


if __name__ == "__main__":
    # 简单测试
    import turtle
    
    # 创建测试环境
    screen = turtle.Screen()
    screen.setup(800, 600)
    screen.bgcolor("white")
    screen.title("五角星绘制测试")
    
    pen = turtle.Turtle()
    pen.speed(5)
    
    # 创建星星绘制器
    drawer = StarDrawer(pen)
    
    # 测试绘制不同大小和位置的星星
    test_stars = [
        ((0, 0), 50, 0.0),           # 中心大星
        ((-100, 100), 20, 0.5),     # 左上小星
        ((100, 100), 20, 1.0),      # 右上小星
        ((-100, -100), 20, 1.5),    # 左下小星
        ((100, -100), 20, 2.0),     # 右下小星
    ]
    
    print("开始绘制测试星星...")
    for i, (center, radius, rotation) in enumerate(test_stars, 1):
        success = drawer.draw_five_pointed_star(center, radius, rotation)
        print(f"星星 {i}: {'✓' if success else '✗'}")
    
    print("测试完成，点击窗口关闭")
    screen.exitonclick()