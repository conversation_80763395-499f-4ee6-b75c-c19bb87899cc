#!/usr/bin/env python3
"""
中华人民共和国国旗绘制程序
使用Python turtle库绘制符合国家标准GB 12982-2004的五星红旗

作者: Kiro AI Assistant
版本: 1.0
"""

import turtle
import math
from dataclasses import dataclass
from typing import Tuple, List

from constants import *
from config import FlagConfig, Star
from star_drawer import StarDrawer


def setup_canvas(config: FlagConfig) -> turtle.Screen:
    """初始化turtle画布和窗口设置
    
    Args:
        config: 国旗绘制配置对象
        
    Returns:
        配置好的turtle屏幕对象
        
    Raises:
        turtle.Terminator: 当turtle窗口被意外关闭时
        Exception: 其他初始化错误
    """
    try:
        # 创建屏幕对象
        screen = turtle.Screen()
        
        # 设置窗口标题
        screen.title(config.window_title)
        
        # 设置窗口大小
        screen.setup(width=config.window_width, height=config.window_height)
        
        # 设置画布背景色
        screen.bgcolor(config.canvas_background)
        
        # 设置颜色模式为255（RGB模式）
        screen.colormode(255)
        
        # 设置坐标系统 - 使用标准坐标系，中心为原点
        screen.setworldcoordinates(
            -config.width/2 - config.window_margin/2,  # 左边界
            -config.height/2 - config.window_margin/2, # 下边界
            config.width/2 + config.window_margin/2,   # 右边界
            config.height/2 + config.window_margin/2   # 上边界
        )
        
        # 关闭自动更新以提高绘制性能
        screen.tracer(0)
        
        print(f"✓ 画布初始化完成")
        print(f"  - 窗口尺寸: {config.window_width} x {config.window_height}")
        print(f"  - 画布背景: {config.canvas_background}")
        print(f"  - 坐标范围: ({-config.width/2:.0f}, {-config.height/2:.0f}) 到 ({config.width/2:.0f}, {config.height/2:.0f})")
        
        return screen
        
    except turtle.Terminator:
        print("✗ Turtle窗口被意外关闭")
        raise
    except Exception as e:
        print(f"✗ 画布初始化失败: {e}")
        raise


def setup_turtle_pen(config: FlagConfig) -> turtle.Turtle:
    """初始化turtle画笔设置
    
    Args:
        config: 国旗绘制配置对象
        
    Returns:
        配置好的turtle画笔对象
        
    Raises:
        Exception: 画笔初始化错误
    """
    try:
        # 创建turtle对象
        pen = turtle.Turtle()
        
        # 设置画笔速度
        pen.speed(config.draw_speed)
        
        # 设置画笔粗细
        pen.pensize(config.pen_size)
        
        # 设置初始位置到屏幕中心
        pen.penup()
        pen.goto(0, 0)
        pen.pendown()
        
        # 设置初始朝向（向右）
        pen.setheading(0)
        
        # 隐藏turtle图标以获得更清晰的绘制效果
        pen.hideturtle()
        
        print(f"✓ 画笔初始化完成")
        print(f"  - 绘制速度: {config.draw_speed}")
        print(f"  - 画笔粗细: {config.pen_size}")
        print(f"  - 初始位置: (0, 0)")
        
        return pen
        
    except Exception as e:
        print(f"✗ 画笔初始化失败: {e}")
        raise


def draw_flag_background(pen: turtle.Turtle, config: FlagConfig):
    """绘制国旗红色背景
    
    Args:
        pen: turtle画笔对象
        config: 国旗绘制配置对象
        
    Raises:
        Exception: 绘制过程中的错误
    """
    try:
        print("开始绘制国旗背景...")
        
        # 移动到国旗左上角
        top_left_x, top_left_y = config.flag_top_left
        pen.penup()
        pen.goto(top_left_x, top_left_y)
        pen.pendown()
        
        # 设置填充颜色为中国红
        pen.fillcolor(config.background_color)
        pen.color(config.background_color)
        
        # 开始填充
        pen.begin_fill()
        
        # 绘制矩形 - 按照3:2比例
        # 向右绘制宽度
        pen.setheading(0)  # 向右
        pen.forward(config.width)
        
        # 向下绘制高度
        pen.setheading(270)  # 向下
        pen.forward(config.height)
        
        # 向左回到起始x坐标
        pen.setheading(180)  # 向左
        pen.forward(config.width)
        
        # 向上回到起始点
        pen.setheading(90)  # 向上
        pen.forward(config.height)
        
        # 结束填充
        pen.end_fill()
        
        print(f"✓ 国旗背景绘制完成")
        print(f"  - 背景颜色: {config.background_color} (标准中国红)")
        print(f"  - 尺寸: {config.width} x {config.height} 像素")
        print(f"  - 长宽比: {config.width/config.height:.1f}:1 (标准3:2)")
        
        # 重置画笔位置到中心
        pen.penup()
        pen.goto(0, 0)
        pen.pendown()
        
    except Exception as e:
        print(f"✗ 国旗背景绘制失败: {e}")
        raise


def draw_large_star(pen: turtle.Turtle, config: FlagConfig) -> bool:
    """绘制大五角星
    
    Args:
        pen: turtle画笔对象
        config: 国旗绘制配置对象
        
    Returns:
        True表示绘制成功，False表示绘制失败
        
    Raises:
        Exception: 绘制过程中的错误
    """
    try:
        print("开始绘制大五角星...")
        
        # 创建星星绘制器
        star_drawer = StarDrawer(pen)
        
        # 获取大星配置
        large_star = config.create_large_star()
        
        print(f"  - 大星中心位置: ({large_star.center[0]:.1f}, {large_star.center[1]:.1f})")
        print(f"  - 大星半径: {large_star.radius:.1f} 像素")
        print(f"  - 大星旋转角度: {large_star.rotation:.2f} 弧度")
        print(f"  - 大星颜色: {large_star.color}")
        
        # 验证星星位置是否在画布范围内
        canvas_bounds = (
            -config.width/2 - config.window_margin/2,
            -config.height/2 - config.window_margin/2,
            config.width/2 + config.window_margin/2,
            config.height/2 + config.window_margin/2
        )
        
        if not star_drawer.validate_star_position(large_star.center, large_star.radius, canvas_bounds):
            print("⚠ 警告: 大星可能超出画布边界")
        
        # 绘制大五角星
        success = star_drawer.draw_star_from_config(large_star)
        
        if success:
            print("✓ 大五角星绘制完成")
            
            # 验证绘制结果
            from geometry import GeometryCalculator
            calc = GeometryCalculator()
            points = calc.calculate_star_points(large_star.center, large_star.radius, large_star.rotation)
            
            if calc.validate_star_geometry(large_star.center, large_star.radius, points):
                print("✓ 大星几何形状验证通过")
            else:
                print("⚠ 警告: 大星几何形状验证失败")
        else:
            print("✗ 大五角星绘制失败")
        
        return success
        
    except Exception as e:
        print(f"✗ 大五角星绘制失败: {e}")
        return False


def draw_small_stars(pen: turtle.Turtle, config: FlagConfig) -> bool:
    """绘制四颗小五角星
    
    Args:
        pen: turtle画笔对象
        config: 国旗绘制配置对象
        
    Returns:
        True表示绘制成功，False表示绘制失败
        
    Raises:
        Exception: 绘制过程中的错误
    """
    try:
        print("开始绘制四颗小五角星...")
        
        # 创建星星绘制器
        star_drawer = StarDrawer(pen)
        
        # 获取小星配置
        small_stars = config.create_small_stars()
        
        print(f"  - 小星数量: {len(small_stars)} 颗")
        print(f"  - 小星半径: {small_stars[0].radius:.1f} 像素")
        print(f"  - 小星颜色: {small_stars[0].color}")
        
        # 验证画布边界
        canvas_bounds = (
            -config.width/2 - config.window_margin/2,
            -config.height/2 - config.window_margin/2,
            config.width/2 + config.window_margin/2,
            config.height/2 + config.window_margin/2
        )
        
        success_count = 0
        
        # 逐个绘制小星
        for i, small_star in enumerate(small_stars, 1):
            print(f"  绘制第 {i} 颗小星:")
            print(f"    - 中心位置: ({small_star.center[0]:.1f}, {small_star.center[1]:.1f})")
            print(f"    - 旋转角度: {small_star.rotation:.3f} 弧度 ({small_star.rotation * 180 / math.pi:.1f}°)")
            
            # 验证星星位置
            if not star_drawer.validate_star_position(small_star.center, small_star.radius, canvas_bounds):
                print(f"    ⚠ 警告: 第 {i} 颗小星可能超出画布边界")
            
            # 绘制小星
            success = star_drawer.draw_star_from_config(small_star)
            
            if success:
                print(f"    ✓ 第 {i} 颗小星绘制完成")
                success_count += 1
                
                # 验证几何形状
                from geometry import GeometryCalculator
                calc = GeometryCalculator()
                points = calc.calculate_star_points(small_star.center, small_star.radius, small_star.rotation)
                
                if calc.validate_star_geometry(small_star.center, small_star.radius, points):
                    print(f"    ✓ 第 {i} 颗小星几何形状验证通过")
                else:
                    print(f"    ⚠ 警告: 第 {i} 颗小星几何形状验证失败")
            else:
                print(f"    ✗ 第 {i} 颗小星绘制失败")
        
        all_success = success_count == len(small_stars)
        
        if all_success:
            print("✓ 所有小五角星绘制完成")
            print("✓ 四颗小星均指向大星中心，符合国家标准")
        else:
            print(f"✗ 小五角星绘制部分失败 ({success_count}/{len(small_stars)})")
        
        return all_success
        
    except Exception as e:
        print(f"✗ 小五角星绘制失败: {e}")
        return False


def cleanup_canvas(screen: turtle.Screen):
    """清理画布资源
    
    Args:
        screen: turtle屏幕对象
    """
    try:
        # 显示最终结果
        screen.update()
        
        # 等待用户点击关闭
        print("\n绘制完成！点击窗口关闭程序...")
        screen.exitonclick()
        
    except turtle.Terminator:
        print("程序正常退出")
    except Exception as e:
        print(f"清理过程中发生错误: {e}")


def main():
    """主程序入口"""
    print("=" * 50)
    print("中华人民共和国国旗绘制程序")
    print("基于国家标准 GB 12982-2004《国旗》")
    print("=" * 50)
    print("正在初始化...")
    
    screen = None
    pen = None
    
    try:
        # 创建默认配置
        config = FlagConfig()
        print(f"✓ 配置加载成功")
        print(f"  - 国旗尺寸: {config.width} x {config.height} 像素")
        print(f"  - 长宽比: {config.width/config.height:.1f}:1 (标准3:2)")
        print(f"  - 绘制速度: {config.draw_speed}")
        
        # 验证配置
        from config import validate_config
        if validate_config(config):
            print("✓ 配置验证通过")
        
        # 显示星星信息
        large_star = config.create_large_star()
        small_stars = config.create_small_stars()
        print(f"✓ 星星配置生成完成")
        print(f"  - 大星半径: {large_star.radius:.1f} 像素")
        print(f"  - 小星半径: {small_stars[0].radius:.1f} 像素")
        print(f"  - 小星数量: {len(small_stars)} 颗")
        
        print("\n开始初始化turtle画布...")
        
        # 初始化画布
        screen = setup_canvas(config)
        
        # 初始化画笔
        pen = setup_turtle_pen(config)
        
        print("\n✓ Turtle环境初始化完成")
        
        # 绘制国旗背景
        draw_flag_background(pen, config)
        
        # 绘制大五角星
        large_star_success = draw_large_star(pen, config)
        
        # 绘制四颗小五角星
        small_stars_success = draw_small_stars(pen, config)
        
        # 更新屏幕显示
        screen.update()
        
        print("\n✓ 国旗背景绘制完成")
        if large_star_success:
            print("✓ 大五角星绘制完成")
        else:
            print("✗ 大五角星绘制失败")
        
        if small_stars_success:
            print("✓ 四颗小五角星绘制完成")
        else:
            print("✗ 小五角星绘制失败")
        
        if large_star_success and small_stars_success:
            print("🎉 中华人民共和国国旗绘制完成！")
            print("   符合国家标准 GB 12982-2004《国旗》规定")
        else:
            print("⚠ 国旗绘制存在问题，请检查实现")
        
        # 清理和退出
        cleanup_canvas(screen)
        
    except turtle.Terminator:
        print("✓ 程序正常退出")
        return 0
    except KeyboardInterrupt:
        print("\n✓ 用户中断程序")
        return 0
    except Exception as e:
        print(f"✗ 程序执行失败: {e}")
        if screen:
            try:
                screen.bye()
            except:
                pass
        return 1
    
    return 0


if __name__ == "__main__":
    main()