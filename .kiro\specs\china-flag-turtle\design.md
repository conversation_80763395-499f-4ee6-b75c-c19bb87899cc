# 设计文档

## 概述

本设计文档描述了使用Python turtle库绘制符合中国国家标准的五星红旗的技术实现方案。设计严格遵循GB 12982-2004标准，确保旗帜的所有元素都按照官方规定的比例、位置和颜色进行绘制。

## 架构

### 整体架构
```
FlagDrawer (主绘制类)
├── Canvas Setup (画布设置)
├── Flag Background (国旗背景)
├── Star Calculator (星星计算器)
├── Large Star Drawer (大星绘制器)
└── Small Stars Drawer (小星绘制器)
```

### 模块分解
- **主控制模块**: 协调整个绘制过程
- **几何计算模块**: 处理所有位置和角度计算
- **绘制模块**: 负责具体的turtle绘制操作
- **常量定义模块**: 存储所有标准规定的数值

## 组件和接口

### 1. 常量定义 (Constants)
```python
# 国旗标准比例和颜色
FLAG_RATIO = 3/2  # 长宽比 3:2
FLAG_RED = "#EE1C25"  # 中国红标准色
STAR_YELLOW = "#FFFF00"  # 金黄色

# 基于国家标准的位置比例
LARGE_STAR_CENTER = (1/4, 1/4)  # 大星中心位置（相对于旗帜尺寸）
LARGE_STAR_RADIUS_RATIO = 3/20  # 大星外接圆半径与旗高的比例
SMALL_STAR_RADIUS_RATIO = 1/20  # 小星外接圆半径与旗高的比例

# 四颗小星的精确位置（基于国家标准）
SMALL_STARS_POSITIONS = [
    (1/2, 1/10),    # 第一颗小星
    (3/5, 1/5),     # 第二颗小星
    (3/5, 3/10),    # 第三颗小星
    (1/2, 2/5)      # 第四颗小星
]
```

### 2. 几何计算器 (GeometryCalculator)
```python
class GeometryCalculator:
    def calculate_star_points(self, center, radius, rotation=0)
    def calculate_pointing_angle(self, from_point, to_point)
    def convert_relative_to_absolute(self, relative_pos, flag_size)
```

**职责:**
- 计算五角星的五个顶点坐标
- 计算小星指向大星的角度
- 坐标系转换（相对位置转绝对位置）

### 3. 国旗绘制器 (FlagDrawer)
```python
class FlagDrawer:
    def __init__(self, width=600)
    def setup_canvas(self)
    def draw_background(self)
    def draw_large_star(self)
    def draw_small_stars(self)
    def draw_complete_flag(self)
```

**职责:**
- 初始化turtle环境
- 协调各个绘制步骤
- 管理绘制状态

### 4. 星星绘制器 (StarDrawer)
```python
class StarDrawer:
    def draw_five_pointed_star(self, turtle_obj, center, radius, rotation=0)
    def move_to_position(self, turtle_obj, position)
    def set_star_style(self, turtle_obj)
```

**职责:**
- 绘制单个五角星
- 处理turtle的移动和样式设置
- 管理绘制的填充和边框

## 数据模型

### 坐标系统
- **屏幕坐标系**: turtle的默认坐标系，原点在屏幕中心
- **国旗坐标系**: 以国旗左上角为原点的相对坐标系
- **标准比例系统**: 基于国家标准的相对位置比例

### 星星数据结构
```python
@dataclass
class Star:
    center: Tuple[float, float]  # 中心坐标
    radius: float                # 外接圆半径
    rotation: float              # 旋转角度（弧度）
    color: str                   # 填充颜色
```

### 国旗配置
```python
@dataclass
class FlagConfig:
    width: int                   # 国旗宽度（像素）
    height: int                  # 国旗高度（像素）
    background_color: str        # 背景颜色
    star_color: str             # 星星颜色
    draw_speed: int             # 绘制速度
```

## 错误处理

### 输入验证
- 验证国旗尺寸参数的合理性
- 检查颜色值的有效性
- 确保turtle环境正确初始化

### 绘制错误处理
```python
try:
    # 绘制操作
    pass
except turtle.Terminator:
    # 处理窗口被意外关闭
    print("绘制窗口被关闭")
except Exception as e:
    # 处理其他绘制错误
    print(f"绘制过程中发生错误: {e}")
```

### 几何计算错误处理
- 处理除零错误
- 验证角度计算结果
- 确保坐标转换的正确性

## 测试策略

### 单元测试
1. **几何计算测试**
   - 测试五角星顶点计算的准确性
   - 验证角度计算函数
   - 测试坐标转换函数

2. **常量验证测试**
   - 验证所有比例常量符合国家标准
   - 测试颜色值的正确性

3. **绘制逻辑测试**
   - 测试星星位置计算
   - 验证旋转角度计算

### 集成测试
1. **完整绘制测试**
   - 测试完整国旗的绘制流程
   - 验证各组件协作的正确性

2. **视觉验证测试**
   - 生成测试图像进行人工验证
   - 对比标准国旗图像

### 性能测试
- 测试不同尺寸下的绘制性能
- 验证内存使用情况

## 实现细节

### 五角星绘制算法
使用数学公式计算五角星的顶点：
```python
def calculate_star_points(center_x, center_y, radius, rotation=0):
    points = []
    for i in range(10):  # 5个外顶点 + 5个内顶点
        angle = rotation + i * math.pi / 5
        if i % 2 == 0:  # 外顶点
            r = radius
        else:  # 内顶点
            r = radius * 0.382  # 黄金比例
        x = center_x + r * math.cos(angle)
        y = center_y + r * math.sin(angle)
        points.append((x, y))
    return points
```

### 小星朝向计算
每颗小星的一个角必须指向大星中心：
```python
def calculate_star_rotation(small_star_center, large_star_center):
    dx = large_star_center[0] - small_star_center[0]
    dy = large_star_center[1] - small_star_center[1]
    angle = math.atan2(dy, dx)
    return angle - math.pi/2  # 调整使顶点指向目标
```

### 绘制优化
- 使用turtle.tracer()控制动画速度
- 预计算所有坐标避免重复计算
- 合理设置turtle的penup/pendown状态

## 用户界面设计

### 窗口设置
- 窗口标题: "中华人民共和国国旗 - Turtle绘制"
- 背景色: 白色或浅灰色以突出国旗
- 窗口大小: 根据国旗尺寸自动调整

### 交互功能
- 支持点击窗口关闭程序
- 可选的绘制速度调节
- 绘制完成后的暂停显示

### 绘制过程可视化
- 显示绘制轨迹
- 逐步绘制各个元素
- 可选的绘制步骤说明文字