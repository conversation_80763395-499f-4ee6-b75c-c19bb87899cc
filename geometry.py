"""
几何计算器模块
处理国旗绘制中的所有几何计算，包括五角星顶点计算、坐标转换、角度计算等

基于国家标准 GB 12982-2004《国旗》的精确几何要求
"""

import math
from typing import Tuple, List
from dataclasses import dataclass

from constants import (
    STAR_OUTER_ANGLE, STAR_INNER_ANGLE, STAR_INNER_RADIUS_RATIO,
    DEG_TO_RAD, RAD_TO_DEG
)


@dataclass
class Point:
    """二维点坐标"""
    x: float
    y: float
    
    def __iter__(self):
        """支持元组解包"""
        yield self.x
        yield self.y
    
    def distance_to(self, other: 'Point') -> float:
        """计算到另一点的距离"""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)


class GeometryCalculator:
    """几何计算器类
    
    提供国旗绘制所需的所有几何计算功能，包括：
    - 五角星顶点坐标计算
    - 坐标系转换
    - 角度计算
    - 几何验证
    """
    
    @staticmethod
    def calculate_star_points(center: Tuple[float, float], 
                            radius: float, 
                            rotation: float = 0.0) -> List[Tuple[float, float]]:
        """计算五角星的所有顶点坐标
        
        Args:
            center: 五角星中心坐标 (x, y)
            radius: 外接圆半径
            rotation: 旋转角度（弧度），0表示顶点指向正上方
            
        Returns:
            包含10个顶点坐标的列表，按绘制顺序排列（外顶点和内顶点交替）
            
        Raises:
            ValueError: 当半径小于等于0时
        """
        if radius <= 0:
            raise ValueError("半径必须大于0")
        
        center_x, center_y = center
        points = []
        
        # 计算10个顶点（5个外顶点 + 5个内顶点）
        for i in range(10):
            # 计算当前顶点的角度
            angle = rotation + i * STAR_OUTER_ANGLE / 2
            
            # 确定是外顶点还是内顶点
            if i % 2 == 0:  # 外顶点
                current_radius = radius
            else:  # 内顶点
                current_radius = radius * STAR_INNER_RADIUS_RATIO
            
            # 计算顶点坐标
            x = center_x + current_radius * math.sin(angle)
            y = center_y + current_radius * math.cos(angle)
            
            points.append((x, y))
        
        return points
    
    @staticmethod
    def calculate_pointing_angle(from_point: Tuple[float, float],
                               to_point: Tuple[float, float]) -> float:
        """计算从一个点指向另一个点的角度

        Args:
            from_point: 起始点坐标 (x, y)
            to_point: 目标点坐标 (x, y)

        Returns:
            角度（弧度），与五角星坐标系统一致（0表示正上方）

        Raises:
            ValueError: 当两点重合时
        """
        from_x, from_y = from_point
        to_x, to_y = to_point

        # 计算向量
        dx = to_x - from_x
        dy = to_y - from_y

        # 检查两点是否重合
        if abs(dx) < 1e-10 and abs(dy) < 1e-10:
            raise ValueError("两点不能重合")

        # 计算角度，与五角星坐标系统一致
        # 在五角星系统中：angle=0时顶点在正上方
        # x = center_x + radius * sin(angle)
        # y = center_y + radius * cos(angle)
        # 因此我们需要使用 atan2(dx, dy) 来获得正确的角度
        angle = math.atan2(dx, dy)

        return angle
    
    @staticmethod
    def convert_relative_to_absolute(relative_pos: Tuple[float, float], 
                                   flag_size: Tuple[int, int],
                                   flag_top_left: Tuple[float, float]) -> Tuple[float, float]:
        """将相对位置转换为绝对坐标
        
        Args:
            relative_pos: 相对位置 (x_ratio, y_ratio)，范围0.0-1.0
            flag_size: 国旗尺寸 (width, height)
            flag_top_left: 国旗左上角在绝对坐标系中的位置
            
        Returns:
            绝对坐标 (x, y)
            
        Raises:
            ValueError: 当相对位置超出有效范围时
        """
        rel_x, rel_y = relative_pos
        flag_width, flag_height = flag_size
        top_left_x, top_left_y = flag_top_left
        
        # 验证相对位置范围
        if not (0.0 <= rel_x <= 1.0) or not (0.0 <= rel_y <= 1.0):
            raise ValueError(f"相对位置必须在0.0-1.0范围内，得到: ({rel_x}, {rel_y})")
        
        # 计算绝对坐标
        abs_x = top_left_x + rel_x * flag_width
        abs_y = top_left_y - rel_y * flag_height  # y轴向下为正
        
        return (abs_x, abs_y)
    
    @staticmethod
    def calculate_star_rotation_for_pointing(star_center: Tuple[float, float],
                                           target_center: Tuple[float, float]) -> float:
        """计算星星旋转角度，使其一个顶点指向目标点
        
        Args:
            star_center: 星星中心坐标
            target_center: 目标点坐标（通常是大星中心）
            
        Returns:
            旋转角度（弧度）
        """
        # 计算指向目标的角度
        pointing_angle = GeometryCalculator.calculate_pointing_angle(
            star_center, target_center
        )
        
        # 由于五角星默认顶点指向正上方（0弧度），
        # 需要调整角度使顶点指向目标
        return pointing_angle
    
    @staticmethod
    def validate_star_geometry(center: Tuple[float, float], 
                             radius: float, 
                             points: List[Tuple[float, float]]) -> bool:
        """验证五角星几何形状的正确性
        
        Args:
            center: 星星中心坐标
            radius: 外接圆半径
            points: 星星顶点列表
            
        Returns:
            True如果几何形状正确，False否则
        """
        if len(points) != 10:
            return False
        
        center_point = Point(*center)
        tolerance = 1e-6  # 浮点数比较容差
        
        # 验证外顶点到中心的距离
        for i in range(0, 10, 2):  # 外顶点索引：0, 2, 4, 6, 8
            point = Point(*points[i])
            distance = center_point.distance_to(point)
            if abs(distance - radius) > tolerance:
                return False
        
        # 验证内顶点到中心的距离
        expected_inner_radius = radius * STAR_INNER_RADIUS_RATIO
        for i in range(1, 10, 2):  # 内顶点索引：1, 3, 5, 7, 9
            point = Point(*points[i])
            distance = center_point.distance_to(point)
            if abs(distance - expected_inner_radius) > tolerance:
                return False
        
        return True
    
    @staticmethod
    def get_star_bounding_box(points: List[Tuple[float, float]]) -> Tuple[float, float, float, float]:
        """获取五角星的边界框
        
        Args:
            points: 星星顶点列表
            
        Returns:
            边界框 (min_x, min_y, max_x, max_y)
        """
        if not points:
            raise ValueError("顶点列表不能为空")
        
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        
        return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))
    
    @staticmethod
    def normalize_angle(angle: float) -> float:
        """将角度标准化到 [0, 2π) 范围内
        
        Args:
            angle: 输入角度（弧度）
            
        Returns:
            标准化后的角度（弧度）
        """
        while angle < 0:
            angle += 2 * math.pi
        while angle >= 2 * math.pi:
            angle -= 2 * math.pi
        return angle
    
    @staticmethod
    def degrees_to_radians(degrees: float) -> float:
        """角度转弧度"""
        return degrees * DEG_TO_RAD
    
    @staticmethod
    def radians_to_degrees(radians: float) -> float:
        """弧度转角度"""
        return radians * RAD_TO_DEG


# 便利函数
def create_star_points(center_x: float, center_y: float, radius: float, rotation: float = 0.0) -> List[Tuple[float, float]]:
    """创建五角星顶点的便利函数"""
    return GeometryCalculator.calculate_star_points((center_x, center_y), radius, rotation)


def point_to_point_angle(x1: float, y1: float, x2: float, y2: float) -> float:
    """计算两点间角度的便利函数"""
    return GeometryCalculator.calculate_pointing_angle((x1, y1), (x2, y2))


if __name__ == "__main__":
    # 简单测试
    calc = GeometryCalculator()
    
    # 测试五角星顶点计算
    center = (0, 0)
    radius = 100
    points = calc.calculate_star_points(center, radius)
    
    print(f"五角星顶点数量: {len(points)}")
    print(f"几何验证结果: {calc.validate_star_geometry(center, radius, points)}")
    
    # 测试角度计算
    angle = calc.calculate_pointing_angle((0, 0), (100, 100))
    print(f"指向角度: {calc.radians_to_degrees(angle):.1f}度")