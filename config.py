"""
国旗绘制配置和数据模型
定义用于国旗绘制的配置类和数据结构
"""

from dataclasses import dataclass, field
from typing import Tuple, Optional
import math

from constants import (
    DEFAULT_FLAG_WIDTH, DEFAULT_FLAG_HEIGHT, FLAG_RATIO,
    FLAG_RED, STAR_YELLOW, CANVAS_BACKGROUND,
    DEFAULT_DRAW_SPEED, DEFAULT_PEN_SIZE
)


@dataclass
class Star:
    """五角星数据模型
    
    表示一个五角星的所有属性，包括位置、大小、旋转角度等
    """
    center: Tuple[float, float]     # 中心坐标 (x, y)
    radius: float                   # 外接圆半径（像素）
    rotation: float = 0.0           # 旋转角度（弧度），0表示顶点指向正上方
    color: str = STAR_YELLOW        # 填充颜色
    outline_color: str = STAR_YELLOW # 边框颜色
    outline_width: int = 1          # 边框宽度
    
    def __post_init__(self):
        """数据验证"""
        if self.radius <= 0:
            raise ValueError("星星半径必须大于0")
        if not isinstance(self.center, (tuple, list)) or len(self.center) != 2:
            raise ValueError("中心坐标必须是包含两个数值的元组")


@dataclass 
class FlagConfig:
    """国旗绘制配置类
    
    包含绘制国旗所需的所有配置参数
    """
    # 尺寸配置
    width: int = DEFAULT_FLAG_WIDTH                    # 国旗宽度（像素）
    height: int = field(init=False)                    # 国旗高度（自动计算）
    
    # 颜色配置
    background_color: str = FLAG_RED                   # 国旗背景颜色
    star_color: str = STAR_YELLOW                      # 星星颜色
    canvas_background: str = CANVAS_BACKGROUND         # 画布背景颜色
    
    # 绘制配置
    draw_speed: int = DEFAULT_DRAW_SPEED               # 绘制速度 (1-10)
    pen_size: int = DEFAULT_PEN_SIZE                   # 画笔粗细
    show_construction_lines: bool = False              # 是否显示辅助构造线
    
    # 窗口配置
    window_title: str = "中华人民共和国国旗 - Turtle绘制"
    window_margin: int = 50                            # 窗口边距
    
    def __post_init__(self):
        """初始化后处理，计算高度并验证参数"""
        # 根据标准比例自动计算高度
        self.height = int(self.width / FLAG_RATIO)
        
        # 参数验证
        self._validate_config()
    
    def _validate_config(self):
        """验证配置参数的有效性"""
        if self.width <= 0:
            raise ValueError("国旗宽度必须大于0")
        
        if not (1 <= self.draw_speed <= 10):
            raise ValueError("绘制速度必须在1-10之间")
        
        if self.pen_size <= 0:
            raise ValueError("画笔粗细必须大于0")
        
        # 验证颜色格式（十六进制颜色验证）
        colors = [self.background_color, self.star_color, self.canvas_background]
        for color in colors:
            if not self._is_valid_hex_color(color):
                raise ValueError(f"颜色格式错误: {color}")
    
    def _is_valid_hex_color(self, color: str) -> bool:
        """验证十六进制颜色格式"""
        if not color.startswith('#') or len(color) != 7:
            return False
        
        # 验证十六进制字符
        hex_chars = color[1:]  # 去掉#
        try:
            int(hex_chars, 16)  # 尝试转换为十六进制数
            return True
        except ValueError:
            return False
    
    @property
    def window_width(self) -> int:
        """计算窗口宽度"""
        return self.width + 2 * self.window_margin
    
    @property
    def window_height(self) -> int:
        """计算窗口高度"""
        return self.height + 2 * self.window_margin
    
    @property
    def flag_center(self) -> Tuple[float, float]:
        """获取国旗中心坐标（相对于turtle坐标系）"""
        return (0.0, 0.0)  # turtle坐标系中心
    
    @property
    def flag_top_left(self) -> Tuple[float, float]:
        """获取国旗左上角坐标（相对于turtle坐标系）"""
        return (-self.width / 2, self.height / 2)
    
    def get_absolute_position(self, relative_x: float, relative_y: float) -> Tuple[float, float]:
        """将相对位置转换为绝对坐标
        
        Args:
            relative_x: 相对于国旗宽度的比例 (0.0-1.0)
            relative_y: 相对于国旗高度的比例 (0.0-1.0)
            
        Returns:
            绝对坐标 (x, y)
        """
        if not (0.0 <= relative_x <= 1.0) or not (0.0 <= relative_y <= 1.0):
            raise ValueError("相对位置必须在0.0-1.0范围内")
        
        # 计算相对于国旗左上角的偏移
        offset_x = relative_x * self.width
        offset_y = relative_y * self.height
        
        # 转换为turtle坐标系（中心为原点，y轴向上为正）
        top_left_x, top_left_y = self.flag_top_left
        abs_x = top_left_x + offset_x
        abs_y = top_left_y - offset_y  # y轴方向相反
        
        return (abs_x, abs_y)
    
    def get_star_radius(self, is_large_star: bool) -> float:
        """获取星星的绝对半径
        
        Args:
            is_large_star: True表示大星，False表示小星
            
        Returns:
            星星半径（像素）
        """
        from constants import LARGE_STAR_RADIUS_RATIO, SMALL_STAR_RADIUS_RATIO
        
        if is_large_star:
            return self.height * LARGE_STAR_RADIUS_RATIO
        else:
            return self.height * SMALL_STAR_RADIUS_RATIO
    
    def create_large_star(self) -> Star:
        """创建大五角星配置"""
        from constants import LARGE_STAR_CENTER_X_RATIO, LARGE_STAR_CENTER_Y_RATIO
        
        center = self.get_absolute_position(
            LARGE_STAR_CENTER_X_RATIO, 
            LARGE_STAR_CENTER_Y_RATIO
        )
        radius = self.get_star_radius(is_large_star=True)
        
        return Star(
            center=center,
            radius=radius,
            rotation=0.0,  # 大星顶点指向正上方
            color=self.star_color
        )
    
    def create_small_stars(self) -> list[Star]:
        """创建四颗小五角星配置"""
        from constants import SMALL_STARS_POSITIONS
        
        large_star = self.create_large_star()
        small_stars = []
        radius = self.get_star_radius(is_large_star=False)
        
        for pos_x, pos_y in SMALL_STARS_POSITIONS:
            center = self.get_absolute_position(pos_x, pos_y)
            
            # 计算小星指向大星的角度
            dx = large_star.center[0] - center[0]
            dy = large_star.center[1] - center[1]
            angle_to_large_star = math.atan2(dy, dx)
            
            # 调整角度使星星的一个顶点指向大星中心
            rotation = angle_to_large_star - math.pi / 2
            
            small_star = Star(
                center=center,
                radius=radius,
                rotation=rotation,
                color=self.star_color
            )
            small_stars.append(small_star)
        
        return small_stars
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"FlagConfig(尺寸={self.width}x{self.height}, "
                f"速度={self.draw_speed}, 背景色={self.background_color})")


# 配置验证函数
def validate_config(config: FlagConfig) -> bool:
    """验证配置对象的完整性"""
    try:
        # 测试创建星星配置
        large_star = config.create_large_star()
        small_stars = config.create_small_stars()
        
        # 验证星星数量
        assert len(small_stars) == 4, "必须有4颗小星"
        
        # 验证星星属性
        assert large_star.radius > 0, "大星半径必须大于0"
        for star in small_stars:
            assert star.radius > 0, "小星半径必须大于0"
        
        print("配置验证通过")
        return True
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False


if __name__ == "__main__":
    # 测试配置类
    config = FlagConfig(width=600)
    print(config)
    validate_config(config)
    
    # 测试星星创建
    large_star = config.create_large_star()
    small_stars = config.create_small_stars()
    
    print(f"大星: 中心{large_star.center}, 半径{large_star.radius:.1f}")
    for i, star in enumerate(small_stars, 1):
        print(f"小星{i}: 中心{star.center}, 半径{star.radius:.1f}, 旋转{star.rotation:.2f}弧度")