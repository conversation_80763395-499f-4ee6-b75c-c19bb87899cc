"""
数据模型单元测试
验证Star和FlagConfig数据模型的正确性和数据验证功能
"""

import unittest
import math
from typing import Tuple

from config import Star, FlagConfig, validate_config
from constants import (
    FLAG_RATIO, FLAG_RED, STAR_YELLOW, CANVAS_BACKGROUND,
    DEFAULT_FLAG_WIDTH, DEFAULT_DRAW_SPEED, DEFAULT_PEN_SIZE,
    LARGE_STAR_CENTER_X_RATIO, LARGE_STAR_CENTER_Y_RATIO,
    LARGE_STAR_RADIUS_RATIO, SMALL_STAR_RADIUS_RATIO,
    SMALL_STARS_POSITIONS
)


class TestStar(unittest.TestCase):
    """Star数据模型测试类"""
    
    def test_star_creation_valid(self):
        """测试有效的星星创建"""
        center = (100, 50)
        radius = 30
        
        star = Star(center=center, radius=radius)
        
        self.assertEqual(star.center, center)
        self.assertEqual(star.radius, radius)
        self.assertEqual(star.rotation, 0.0)  # 默认值
        self.assertEqual(star.color, STAR_YELLOW)  # 默认值
        self.assertEqual(star.outline_color, STAR_YELLOW)  # 默认值
        self.assertEqual(star.outline_width, 1)  # 默认值
    
    def test_star_creation_with_all_params(self):
        """测试带所有参数的星星创建"""
        center = (200, 100)
        radius = 50
        rotation = math.pi / 4
        color = "#FF0000"
        outline_color = "#00FF00"
        outline_width = 2
        
        star = Star(
            center=center,
            radius=radius,
            rotation=rotation,
            color=color,
            outline_color=outline_color,
            outline_width=outline_width
        )
        
        self.assertEqual(star.center, center)
        self.assertEqual(star.radius, radius)
        self.assertEqual(star.rotation, rotation)
        self.assertEqual(star.color, color)
        self.assertEqual(star.outline_color, outline_color)
        self.assertEqual(star.outline_width, outline_width)
    
    def test_star_invalid_radius(self):
        """测试无效半径的错误处理"""
        center = (0, 0)
        
        # 零半径
        with self.assertRaises(ValueError) as context:
            Star(center=center, radius=0)
        self.assertIn("半径必须大于0", str(context.exception))
        
        # 负半径
        with self.assertRaises(ValueError) as context:
            Star(center=center, radius=-10)
        self.assertIn("半径必须大于0", str(context.exception))
    
    def test_star_invalid_center(self):
        """测试无效中心坐标的错误处理"""
        radius = 50
        
        # 非元组/列表
        with self.assertRaises(ValueError) as context:
            Star(center="invalid", radius=radius)
        self.assertIn("中心坐标必须是包含两个数值的元组", str(context.exception))
        
        # 长度不正确
        with self.assertRaises(ValueError) as context:
            Star(center=(1, 2, 3), radius=radius)
        self.assertIn("中心坐标必须是包含两个数值的元组", str(context.exception))
        
        # 空元组
        with self.assertRaises(ValueError) as context:
            Star(center=(), radius=radius)
        self.assertIn("中心坐标必须是包含两个数值的元组", str(context.exception))


class TestFlagConfig(unittest.TestCase):
    """FlagConfig配置类测试"""
    
    def test_flag_config_creation_default(self):
        """测试默认配置创建"""
        config = FlagConfig()
        
        self.assertEqual(config.width, DEFAULT_FLAG_WIDTH)
        self.assertEqual(config.height, int(DEFAULT_FLAG_WIDTH / FLAG_RATIO))
        self.assertEqual(config.background_color, FLAG_RED)
        self.assertEqual(config.star_color, STAR_YELLOW)
        self.assertEqual(config.canvas_background, CANVAS_BACKGROUND)
        self.assertEqual(config.draw_speed, DEFAULT_DRAW_SPEED)
        self.assertEqual(config.pen_size, DEFAULT_PEN_SIZE)
        self.assertFalse(config.show_construction_lines)
        self.assertEqual(config.window_margin, 50)
    
    def test_flag_config_creation_custom(self):
        """测试自定义配置创建"""
        width = 900
        background_color = "#FF0000"
        star_color = "#00FF00"
        draw_speed = 8
        pen_size = 2
        
        config = FlagConfig(
            width=width,
            background_color=background_color,
            star_color=star_color,
            draw_speed=draw_speed,
            pen_size=pen_size,
            show_construction_lines=True
        )
        
        self.assertEqual(config.width, width)
        self.assertEqual(config.height, int(width / FLAG_RATIO))
        self.assertEqual(config.background_color, background_color)
        self.assertEqual(config.star_color, star_color)
        self.assertEqual(config.draw_speed, draw_speed)
        self.assertEqual(config.pen_size, pen_size)
        self.assertTrue(config.show_construction_lines)
    
    def test_flag_config_height_calculation(self):
        """测试高度自动计算"""
        test_widths = [300, 600, 900, 1200]
        
        for width in test_widths:
            with self.subTest(width=width):
                config = FlagConfig(width=width)
                expected_height = int(width / FLAG_RATIO)
                self.assertEqual(config.height, expected_height)
                self.assertAlmostEqual(config.width / config.height, FLAG_RATIO, places=1)
    
    def test_flag_config_invalid_width(self):
        """测试无效宽度的错误处理"""
        with self.assertRaises(ValueError) as context:
            FlagConfig(width=0)
        self.assertIn("国旗宽度必须大于0", str(context.exception))
        
        with self.assertRaises(ValueError) as context:
            FlagConfig(width=-100)
        self.assertIn("国旗宽度必须大于0", str(context.exception))
    
    def test_flag_config_invalid_draw_speed(self):
        """测试无效绘制速度的错误处理"""
        with self.assertRaises(ValueError) as context:
            FlagConfig(draw_speed=0)
        self.assertIn("绘制速度必须在1-10之间", str(context.exception))
        
        with self.assertRaises(ValueError) as context:
            FlagConfig(draw_speed=11)
        self.assertIn("绘制速度必须在1-10之间", str(context.exception))
    
    def test_flag_config_invalid_pen_size(self):
        """测试无效画笔粗细的错误处理"""
        with self.assertRaises(ValueError) as context:
            FlagConfig(pen_size=0)
        self.assertIn("画笔粗细必须大于0", str(context.exception))
        
        with self.assertRaises(ValueError) as context:
            FlagConfig(pen_size=-1)
        self.assertIn("画笔粗细必须大于0", str(context.exception))
    
    def test_flag_config_invalid_color_format(self):
        """测试无效颜色格式的错误处理"""
        invalid_colors = [
            "red",           # 非十六进制
            "#FF",           # 长度不够
            "#GGGGGG",       # 无效字符
            "FF0000",        # 缺少#
            "#FF00000",      # 长度过长
        ]
        
        for invalid_color in invalid_colors:
            with self.subTest(color=invalid_color):
                with self.assertRaises(ValueError) as context:
                    FlagConfig(background_color=invalid_color)
                self.assertIn("颜色格式错误", str(context.exception))
    
    def test_window_size_properties(self):
        """测试窗口尺寸属性"""
        config = FlagConfig(width=600)
        
        expected_window_width = 600 + 2 * config.window_margin
        expected_window_height = config.height + 2 * config.window_margin
        
        self.assertEqual(config.window_width, expected_window_width)
        self.assertEqual(config.window_height, expected_window_height)
    
    def test_flag_position_properties(self):
        """测试国旗位置属性"""
        config = FlagConfig(width=600)
        
        # 测试中心坐标
        self.assertEqual(config.flag_center, (0.0, 0.0))
        
        # 测试左上角坐标
        expected_top_left = (-config.width / 2, config.height / 2)
        self.assertEqual(config.flag_top_left, expected_top_left)
    
    def test_get_absolute_position(self):
        """测试相对位置转绝对坐标转换"""
        config = FlagConfig(width=600)  # height = 400
        
        test_cases = [
            ((0, 0), (-300, 200)),      # 左上角
            ((1, 0), (300, 200)),       # 右上角
            ((0, 1), (-300, -200)),     # 左下角
            ((1, 1), (300, -200)),      # 右下角
            ((0.5, 0.5), (0, 0)),       # 中心点
            ((0.25, 0.25), (-150, 100)), # 大星位置
        ]
        
        for relative_pos, expected_abs in test_cases:
            with self.subTest(relative_pos=relative_pos):
                result = config.get_absolute_position(*relative_pos)
                self.assertAlmostEqual(result[0], expected_abs[0], places=6)
                self.assertAlmostEqual(result[1], expected_abs[1], places=6)
    
    def test_get_absolute_position_invalid_range(self):
        """测试无效相对位置的错误处理"""
        config = FlagConfig()
        
        invalid_positions = [
            (-0.1, 0.5),  # x < 0
            (1.1, 0.5),   # x > 1
            (0.5, -0.1),  # y < 0
            (0.5, 1.1),   # y > 1
        ]
        
        for invalid_pos in invalid_positions:
            with self.subTest(invalid_pos=invalid_pos):
                with self.assertRaises(ValueError):
                    config.get_absolute_position(*invalid_pos)
    
    def test_get_star_radius(self):
        """测试星星半径计算"""
        config = FlagConfig(width=600)  # height = 400
        
        # 大星半径
        large_radius = config.get_star_radius(is_large_star=True)
        expected_large = config.height * LARGE_STAR_RADIUS_RATIO
        self.assertAlmostEqual(large_radius, expected_large, places=6)
        
        # 小星半径
        small_radius = config.get_star_radius(is_large_star=False)
        expected_small = config.height * SMALL_STAR_RADIUS_RATIO
        self.assertAlmostEqual(small_radius, expected_small, places=6)
        
        # 大星应该比小星大
        self.assertGreater(large_radius, small_radius)
    
    def test_create_large_star(self):
        """测试大星创建"""
        config = FlagConfig(width=600)
        large_star = config.create_large_star()
        
        # 验证类型
        self.assertIsInstance(large_star, Star)
        
        # 验证位置
        expected_center = config.get_absolute_position(
            LARGE_STAR_CENTER_X_RATIO, LARGE_STAR_CENTER_Y_RATIO
        )
        self.assertEqual(large_star.center, expected_center)
        
        # 验证半径
        expected_radius = config.get_star_radius(is_large_star=True)
        self.assertAlmostEqual(large_star.radius, expected_radius, places=6)
        
        # 验证旋转角度（大星顶点指向正上方）
        self.assertEqual(large_star.rotation, 0.0)
        
        # 验证颜色
        self.assertEqual(large_star.color, config.star_color)
    
    def test_create_small_stars(self):
        """测试小星创建"""
        config = FlagConfig(width=600)
        small_stars = config.create_small_stars()
        
        # 验证数量
        self.assertEqual(len(small_stars), 4)
        
        # 验证每颗小星
        expected_radius = config.get_star_radius(is_large_star=False)
        large_star = config.create_large_star()
        
        for i, (star, expected_pos) in enumerate(zip(small_stars, SMALL_STARS_POSITIONS)):
            with self.subTest(star_index=i):
                # 验证类型
                self.assertIsInstance(star, Star)
                
                # 验证位置
                expected_center = config.get_absolute_position(*expected_pos)
                self.assertEqual(star.center, expected_center)
                
                # 验证半径
                self.assertAlmostEqual(star.radius, expected_radius, places=6)
                
                # 验证颜色
                self.assertEqual(star.color, config.star_color)
                
                # 验证旋转角度（应该指向大星）
                dx = large_star.center[0] - star.center[0]
                dy = large_star.center[1] - star.center[1]
                expected_angle = math.atan2(dy, dx) - math.pi / 2
                self.assertAlmostEqual(star.rotation, expected_angle, places=6)
    
    def test_config_string_representation(self):
        """测试配置的字符串表示"""
        config = FlagConfig(width=600)
        str_repr = str(config)
        
        self.assertIn("FlagConfig", str_repr)
        self.assertIn("600x400", str_repr)
        self.assertIn(str(config.draw_speed), str_repr)
        self.assertIn(config.background_color, str_repr)


class TestConfigValidation(unittest.TestCase):
    """配置验证功能测试"""
    
    def test_validate_config_valid(self):
        """测试有效配置的验证"""
        config = FlagConfig(width=600)
        result = validate_config(config)
        self.assertTrue(result)
    
    def test_validate_config_captures_output(self):
        """测试配置验证的输出捕获"""
        import io
        import sys
        
        # 捕获标准输出
        captured_output = io.StringIO()
        sys.stdout = captured_output
        
        try:
            config = FlagConfig(width=600)
            validate_config(config)
            
            output = captured_output.getvalue()
            self.assertIn("配置验证通过", output)
        finally:
            sys.stdout = sys.__stdout__
    
    def test_star_creation_consistency(self):
        """测试星星创建的一致性"""
        config = FlagConfig(width=600)
        
        # 多次创建应该得到相同结果
        large_star1 = config.create_large_star()
        large_star2 = config.create_large_star()
        
        self.assertEqual(large_star1.center, large_star2.center)
        self.assertEqual(large_star1.radius, large_star2.radius)
        self.assertEqual(large_star1.rotation, large_star2.rotation)
        
        small_stars1 = config.create_small_stars()
        small_stars2 = config.create_small_stars()
        
        self.assertEqual(len(small_stars1), len(small_stars2))
        for s1, s2 in zip(small_stars1, small_stars2):
            self.assertEqual(s1.center, s2.center)
            self.assertEqual(s1.radius, s2.radius)
            self.assertAlmostEqual(s1.rotation, s2.rotation, places=6)


class TestDataModelIntegration(unittest.TestCase):
    """数据模型集成测试"""
    
    def test_different_flag_sizes(self):
        """测试不同国旗尺寸下的数据模型"""
        test_widths = [300, 600, 900, 1200]
        
        for width in test_widths:
            with self.subTest(width=width):
                config = FlagConfig(width=width)
                
                # 验证比例保持正确
                self.assertAlmostEqual(config.width / config.height, FLAG_RATIO, places=1)
                
                # 验证星星创建
                large_star = config.create_large_star()
                small_stars = config.create_small_stars()
                
                # 验证星星半径比例
                expected_large_radius = config.height * LARGE_STAR_RADIUS_RATIO
                expected_small_radius = config.height * SMALL_STAR_RADIUS_RATIO
                
                self.assertAlmostEqual(large_star.radius, expected_large_radius, places=6)
                for star in small_stars:
                    self.assertAlmostEqual(star.radius, expected_small_radius, places=6)
    
    def test_star_positioning_accuracy(self):
        """测试星星定位的准确性"""
        config = FlagConfig(width=600)
        large_star = config.create_large_star()
        small_stars = config.create_small_stars()
        
        # 验证大星位置符合标准
        expected_large_center = config.get_absolute_position(
            LARGE_STAR_CENTER_X_RATIO, LARGE_STAR_CENTER_Y_RATIO
        )
        self.assertEqual(large_star.center, expected_large_center)
        
        # 验证小星位置符合标准
        for star, expected_pos in zip(small_stars, SMALL_STARS_POSITIONS):
            expected_center = config.get_absolute_position(*expected_pos)
            self.assertEqual(star.center, expected_center)
        
        # 验证小星都在大星右侧（符合国旗标准）
        for star in small_stars:
            self.assertGreater(star.center[0], large_star.center[0])


if __name__ == "__main__":
    # 运行所有测试
    unittest.main(verbosity=2)