#!/usr/bin/env python3
"""
测试turtle画布设置和背景绘制功能
验证任务3.1和3.2的实现
"""

import unittest
import turtle
import time
from unittest.mock import patch, MagicMock

from china_flag_turtle import setup_canvas, setup_turtle_pen, draw_flag_background
from config import FlagConfig
from constants import FLAG_RED, STAR_YELLOW


class TestCanvasAndBackground(unittest.TestCase):
    """测试画布设置和背景绘制功能"""
    
    def setUp(self):
        """测试前准备"""
        self.config = FlagConfig(width=300)  # 使用较小尺寸进行测试
    
    def test_flag_config_properties(self):
        """测试配置对象的属性"""
        self.assertEqual(self.config.width, 300)
        self.assertEqual(self.config.height, 200)  # 3:2比例
        self.assertEqual(self.config.background_color, FLAG_RED)
        self.assertEqual(self.config.star_color, STAR_YELLOW)
        self.assertEqual(self.config.window_width, 400)  # 300 + 2*50
        self.assertEqual(self.config.window_height, 300)  # 200 + 2*50
    
    def test_flag_coordinates(self):
        """测试国旗坐标计算"""
        top_left = self.config.flag_top_left
        self.assertEqual(top_left, (-150.0, 100.0))  # (-width/2, height/2)
        
        center = self.config.flag_center
        self.assertEqual(center, (0.0, 0.0))
    
    def test_absolute_position_conversion(self):
        """测试相对位置转换为绝对坐标"""
        # 测试左上角 (0, 0)
        abs_pos = self.config.get_absolute_position(0.0, 0.0)
        expected = (-150.0, 100.0)  # 国旗左上角
        self.assertEqual(abs_pos, expected)
        
        # 测试右下角 (1, 1)
        abs_pos = self.config.get_absolute_position(1.0, 1.0)
        expected = (150.0, -100.0)  # 国旗右下角
        self.assertEqual(abs_pos, expected)
        
        # 测试中心 (0.5, 0.5)
        abs_pos = self.config.get_absolute_position(0.5, 0.5)
        expected = (0.0, 0.0)  # 国旗中心
        self.assertEqual(abs_pos, expected)
    
    def test_star_radius_calculation(self):
        """测试星星半径计算"""
        large_radius = self.config.get_star_radius(is_large_star=True)
        small_radius = self.config.get_star_radius(is_large_star=False)
        
        # 大星半径应该是高度的3/20
        expected_large = 200 * 3 / 20  # 30.0
        self.assertEqual(large_radius, expected_large)
        
        # 小星半径应该是高度的1/20
        expected_small = 200 * 1 / 20  # 10.0
        self.assertEqual(small_radius, expected_small)
        
        # 大星应该比小星大
        self.assertGreater(large_radius, small_radius)
    
    def test_color_validation(self):
        """测试颜色格式验证"""
        # 测试有效颜色
        valid_config = FlagConfig(width=300)
        self.assertTrue(valid_config._is_valid_hex_color("#EE1C25"))
        self.assertTrue(valid_config._is_valid_hex_color("#FFFF00"))
        self.assertTrue(valid_config._is_valid_hex_color("#FFFFFF"))
        
        # 测试无效颜色格式
        self.assertFalse(valid_config._is_valid_hex_color("EE1C25"))  # 缺少#
        self.assertFalse(valid_config._is_valid_hex_color("#EE1C2"))  # 长度不对
        self.assertFalse(valid_config._is_valid_hex_color("#GG1C25"))  # 非十六进制字符
    
    def test_config_validation_errors(self):
        """测试配置验证错误处理"""
        # 测试无效宽度
        with self.assertRaises(ValueError):
            FlagConfig(width=0)
        
        with self.assertRaises(ValueError):
            FlagConfig(width=-100)
        
        # 测试无效绘制速度
        with self.assertRaises(ValueError):
            FlagConfig(width=300, draw_speed=0)
        
        with self.assertRaises(ValueError):
            FlagConfig(width=300, draw_speed=11)
    
    @patch('turtle.Screen')
    def test_setup_canvas_mock(self, mock_screen_class):
        """使用mock测试画布设置（避免实际创建窗口）"""
        mock_screen = MagicMock()
        mock_screen_class.return_value = mock_screen
        
        # 调用画布设置函数
        result = setup_canvas(self.config)
        
        # 验证调用
        mock_screen.title.assert_called_once_with(self.config.window_title)
        mock_screen.setup.assert_called_once_with(
            width=self.config.window_width, 
            height=self.config.window_height
        )
        mock_screen.bgcolor.assert_called_once_with(self.config.canvas_background)
        mock_screen.colormode.assert_called_once_with(255)
        mock_screen.tracer.assert_called_once_with(0)
        
        self.assertEqual(result, mock_screen)
    
    @patch('turtle.Turtle')
    def test_setup_turtle_pen_mock(self, mock_turtle_class):
        """使用mock测试画笔设置"""
        mock_pen = MagicMock()
        mock_turtle_class.return_value = mock_pen
        
        # 调用画笔设置函数
        result = setup_turtle_pen(self.config)
        
        # 验证调用
        mock_pen.speed.assert_called_once_with(self.config.draw_speed)
        mock_pen.pensize.assert_called_once_with(self.config.pen_size)
        mock_pen.penup.assert_called()
        mock_pen.goto.assert_called_with(0, 0)
        mock_pen.pendown.assert_called()
        mock_pen.setheading.assert_called_with(0)
        mock_pen.hideturtle.assert_called_once()
        
        self.assertEqual(result, mock_pen)
    
    def test_background_drawing_logic(self):
        """测试背景绘制逻辑（不实际绘制）"""
        # 创建mock画笔
        mock_pen = MagicMock()
        
        # 调用背景绘制函数
        draw_flag_background(mock_pen, self.config)
        
        # 验证关键调用
        mock_pen.fillcolor.assert_called_with(self.config.background_color)
        mock_pen.color.assert_called_with(self.config.background_color)
        mock_pen.begin_fill.assert_called_once()
        mock_pen.end_fill.assert_called_once()
        
        # 验证移动到正确位置
        expected_top_left = self.config.flag_top_left
        mock_pen.goto.assert_any_call(expected_top_left[0], expected_top_left[1])
        
        # 验证绘制了四条边（四次forward调用）
        self.assertEqual(mock_pen.forward.call_count, 4)
        
        # 验证设置了正确的方向（四次setheading调用）
        expected_headings = [0, 270, 180, 90]  # 右、下、左、上
        actual_headings = [call[0][0] for call in mock_pen.setheading.call_args_list[-4:]]
        self.assertEqual(actual_headings, expected_headings)


def run_visual_test():
    """运行可视化测试（需要手动验证）"""
    print("开始可视化测试...")
    print("将显示一个红色矩形背景，请手动验证：")
    print("1. 背景颜色是否为中国红")
    print("2. 长宽比是否为3:2")
    print("3. 窗口标题是否正确")
    
    try:
        config = FlagConfig(width=400)
        screen = setup_canvas(config)
        pen = setup_turtle_pen(config)
        draw_flag_background(pen, config)
        
        screen.update()
        print("✓ 可视化测试完成，请检查窗口显示")
        print("点击窗口关闭...")
        screen.exitonclick()
        
    except Exception as e:
        print(f"✗ 可视化测试失败: {e}")


if __name__ == "__main__":
    print("=" * 50)
    print("画布设置和背景绘制功能测试")
    print("=" * 50)
    
    # 运行单元测试
    print("\n1. 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 询问是否运行可视化测试
    print("\n2. 可视化测试")
    response = input("是否运行可视化测试？(y/n): ").lower().strip()
    if response in ['y', 'yes', '是']:
        run_visual_test()
    else:
        print("跳过可视化测试")
    
    print("\n测试完成！")