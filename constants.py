"""
中华人民共和国国旗标准常量定义
基于国家标准 GB 12982-2004《国旗》

所有比例和位置数据严格遵循国家标准规定
"""

import math
from typing import Tuple

# ==================== 国旗基本比例 ====================
# 国家标准规定：国旗长宽比为3:2
FLAG_RATIO = 3.0 / 2.0  # 长宽比 3:2

# ==================== 标准颜色定义 ====================
# 基于国家标准GB 12982-2004规定的标准色彩
FLAG_RED = "#EE1C25"      # 中国红标准色 (RGB: 238, 28, 37)
STAR_YELLOW = "#FFD700"   # 金黄色 (RGB: 255, 215, 0) - 更符合国家标准的金黄色
CANVAS_BACKGROUND = "#FFFFFF"  # 画布背景色（白色）

# ==================== 大五角星规格 ====================
# 大星中心位置（相对于国旗尺寸的比例）
# 国家标准：距左边1/4宽度，距上边1/4高度
LARGE_STAR_CENTER_X_RATIO = 1.0 / 4.0  # 距左边1/4宽度
LARGE_STAR_CENTER_Y_RATIO = 1.0 / 4.0  # 距上边1/4高度

# 大星外接圆直径为旗高的3/10，因此半径为3/20
LARGE_STAR_RADIUS_RATIO = 3.0 / 20.0  # 大星外接圆半径与旗高的比例

# ==================== 小五角星规格 ====================
# 小星外接圆直径为旗高的1/10，因此半径为1/20
SMALL_STAR_RADIUS_RATIO = 1.0 / 20.0  # 小星外接圆半径与旗高的比例

# 四颗小星的精确位置（基于国家标准GB 12982-2004）
# 位置以相对于国旗尺寸的比例表示 (x_ratio, y_ratio)
# 四颗小星呈弧形排列在大星右侧，每颗小星的一个角尖正对大星的中心点
SMALL_STARS_POSITIONS: Tuple[Tuple[float, float], ...] = (
    (1.0/2.0, 1.0/10.0),    # 第一颗小星：距左边1/2宽度，距上边1/10高度
    (3.0/5.0, 1.0/5.0),     # 第二颗小星：距左边3/5宽度，距上边1/5高度  
    (3.0/5.0, 3.0/10.0),    # 第三颗小星：距左边3/5宽度，距上边3/10高度
    (1.0/2.0, 2.0/5.0)      # 第四颗小星：距左边1/2宽度，距上边2/5高度
)

# ==================== 几何计算常量 ====================
# 五角星内角和外角的数学常量
STAR_OUTER_ANGLE = 2 * math.pi / 5      # 五角星外顶点间的角度
STAR_INNER_ANGLE = math.pi / 5           # 五角星内顶点的角度
GOLDEN_RATIO = 0.618033988749           # 黄金比例，用于计算五角星内顶点距离
STAR_INNER_RADIUS_RATIO = 0.382         # 五角星内顶点半径与外顶点半径的比例

# ==================== 绘制参数 ====================
# turtle绘制相关的默认参数
DEFAULT_DRAW_SPEED = 6          # 默认绘制速度 (1-10)
DEFAULT_PEN_SIZE = 1            # 默认画笔粗细
WINDOW_MARGIN = 50              # 窗口边距（像素）

# ==================== 默认尺寸 ====================
# 默认国旗尺寸（像素）
DEFAULT_FLAG_WIDTH = 600        # 默认宽度
DEFAULT_FLAG_HEIGHT = int(DEFAULT_FLAG_WIDTH / FLAG_RATIO)  # 根据比例计算高度

# ==================== 角度转换 ====================
# 角度和弧度转换常量
DEG_TO_RAD = math.pi / 180.0    # 度转弧度
RAD_TO_DEG = 180.0 / math.pi    # 弧度转度

# ==================== 验证函数 ====================
def validate_constants():
    """验证常量定义的正确性"""
    assert FLAG_RATIO == 1.5, "国旗长宽比必须为3:2"
    assert len(SMALL_STARS_POSITIONS) == 4, "必须定义4颗小星的位置"
    assert DEFAULT_FLAG_HEIGHT == DEFAULT_FLAG_WIDTH / FLAG_RATIO, "默认尺寸比例错误"
    print("常量验证通过")


if __name__ == "__main__":
    validate_constants()
    print("所有常量定义正确")