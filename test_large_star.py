"""
大五角星绘制功能单元测试
验证大星的位置、尺寸和绘制准确性
"""

import unittest
import math
from unittest.mock import Mock, patch

from config import FlagConfig, Star
from star_drawer import StarDrawer
from geometry import GeometryCalculator
from constants import (
    LARGE_STAR_CENTER_X_RATIO, LARGE_STAR_CENTER_Y_RATIO,
    LARGE_STAR_RADIUS_RATIO, STAR_YELLOW
)


class TestLargeStarDrawing(unittest.TestCase):
    """大五角星绘制测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = FlagConfig(width=600)  # 标准尺寸
        self.mock_pen = Mock()
        self.star_drawer = StarDrawer(self.mock_pen)
        self.geometry_calc = GeometryCalculator()
    
    def test_large_star_position_calculation(self):
        """测试大星位置计算的准确性"""
        large_star = self.config.create_large_star()
        
        # 验证中心位置符合国家标准
        expected_x = -self.config.width/2 + LARGE_STAR_CENTER_X_RATIO * self.config.width
        expected_y = self.config.height/2 - LARGE_STAR_CENTER_Y_RATIO * self.config.height
        
        self.assertAlmostEqual(large_star.center[0], expected_x, places=1,
                              msg="大星X坐标不符合标准")
        self.assertAlmostEqual(large_star.center[1], expected_y, places=1,
                              msg="大星Y坐标不符合标准")
        
        print(f"✓ 大星位置验证通过: ({large_star.center[0]:.1f}, {large_star.center[1]:.1f})")
    
    def test_large_star_size_calculation(self):
        """测试大星尺寸计算的准确性"""
        large_star = self.config.create_large_star()
        
        # 验证半径符合国家标准（外接圆直径为旗高的3/10）
        expected_radius = self.config.height * LARGE_STAR_RADIUS_RATIO
        
        self.assertAlmostEqual(large_star.radius, expected_radius, places=1,
                              msg="大星半径不符合标准")
        
        print(f"✓ 大星尺寸验证通过: 半径 {large_star.radius:.1f} 像素")
    
    def test_large_star_rotation(self):
        """测试大星旋转角度（应该指向正上方）"""
        large_star = self.config.create_large_star()
        
        # 大星顶点应该指向正上方（旋转角度为0）
        self.assertEqual(large_star.rotation, 0.0,
                        msg="大星旋转角度应该为0（顶点指向正上方）")
        
        print("✓ 大星旋转角度验证通过: 顶点指向正上方")
    
    def test_large_star_color(self):
        """测试大星颜色设置"""
        large_star = self.config.create_large_star()
        
        # 验证颜色为标准金黄色
        self.assertEqual(large_star.color, STAR_YELLOW,
                        msg="大星颜色应该为标准金黄色")
        
        print(f"✓ 大星颜色验证通过: {large_star.color}")
    
    def test_large_star_geometry_validation(self):
        """测试大星几何形状的准确性"""
        large_star = self.config.create_large_star()
        
        # 计算五角星顶点
        points = self.geometry_calc.calculate_star_points(
            large_star.center, large_star.radius, large_star.rotation
        )
        
        # 验证几何形状
        is_valid = self.geometry_calc.validate_star_geometry(
            large_star.center, large_star.radius, points
        )
        
        self.assertTrue(is_valid, msg="大星几何形状验证失败")
        self.assertEqual(len(points), 10, msg="五角星应该有10个顶点")
        
        print("✓ 大星几何形状验证通过")
    
    def test_large_star_drawing_calls(self):
        """测试大星绘制过程中的turtle调用"""
        large_star = self.config.create_large_star()
        
        # 执行绘制
        success = self.star_drawer.draw_star_from_config(large_star)
        
        # 验证绘制成功
        self.assertTrue(success, msg="大星绘制应该成功")
        
        # 验证turtle方法被正确调用
        self.mock_pen.penup.assert_called()
        self.mock_pen.pendown.assert_called()
        self.mock_pen.goto.assert_called()
        self.mock_pen.begin_fill.assert_called()
        self.mock_pen.end_fill.assert_called()
        self.mock_pen.fillcolor.assert_called_with(STAR_YELLOW)
        
        print("✓ 大星绘制调用验证通过")
    
    def test_large_star_position_ratios(self):
        """测试大星位置比例的精确性"""
        # 测试不同尺寸下的位置比例
        test_sizes = [300, 600, 900, 1200]
        
        for width in test_sizes:
            config = FlagConfig(width=width)
            large_star = config.create_large_star()
            
            # 计算相对位置比例
            flag_top_left = config.flag_top_left
            relative_x = (large_star.center[0] - flag_top_left[0]) / config.width
            relative_y = (flag_top_left[1] - large_star.center[1]) / config.height
            
            # 验证比例符合标准
            self.assertAlmostEqual(relative_x, LARGE_STAR_CENTER_X_RATIO, places=3,
                                  msg=f"宽度{width}时大星X比例不正确")
            self.assertAlmostEqual(relative_y, LARGE_STAR_CENTER_Y_RATIO, places=3,
                                  msg=f"宽度{width}时大星Y比例不正确")
        
        print("✓ 大星位置比例验证通过（多种尺寸）")
    
    def test_large_star_radius_ratio(self):
        """测试大星半径比例的精确性"""
        # 测试不同尺寸下的半径比例
        test_sizes = [300, 600, 900, 1200]
        
        for width in test_sizes:
            config = FlagConfig(width=width)
            large_star = config.create_large_star()
            
            # 计算半径比例
            radius_ratio = large_star.radius / config.height
            
            # 验证比例符合标准
            self.assertAlmostEqual(radius_ratio, LARGE_STAR_RADIUS_RATIO, places=3,
                                  msg=f"宽度{width}时大星半径比例不正确")
        
        print("✓ 大星半径比例验证通过（多种尺寸）")
    
    def test_large_star_boundary_validation(self):
        """测试大星边界验证"""
        large_star = self.config.create_large_star()
        
        # 定义画布边界
        canvas_bounds = (
            -self.config.width/2 - 50,
            -self.config.height/2 - 50,
            self.config.width/2 + 50,
            self.config.height/2 + 50
        )
        
        # 验证大星在画布范围内
        is_within_bounds = self.star_drawer.validate_star_position(
            large_star.center, large_star.radius, canvas_bounds
        )
        
        self.assertTrue(is_within_bounds, msg="大星应该在画布范围内")
        
        print("✓ 大星边界验证通过")


class TestLargeStarIntegration(unittest.TestCase):
    """大五角星集成测试类"""
    
    def test_large_star_with_flag_background(self):
        """测试大星与国旗背景的集成"""
        config = FlagConfig(width=600)
        large_star = config.create_large_star()
        
        # 验证大星位置相对于国旗的正确性
        flag_center = config.flag_center
        flag_top_left = config.flag_top_left
        
        # 大星应该在国旗的左上象限
        self.assertLess(large_star.center[0], flag_center[0],
                       msg="大星应该在国旗中心左侧")
        self.assertGreater(large_star.center[1], flag_center[1],
                          msg="大星应该在国旗中心上方")
        
        print("✓ 大星与国旗背景集成验证通过")
    
    def test_large_star_standard_compliance(self):
        """测试大星符合国家标准的完整性"""
        config = FlagConfig(width=600)
        large_star = config.create_large_star()
        
        # 综合验证所有标准要求
        checks = {
            "位置X比例": abs((large_star.center[0] - config.flag_top_left[0]) / config.width - LARGE_STAR_CENTER_X_RATIO) < 0.001,
            "位置Y比例": abs((config.flag_top_left[1] - large_star.center[1]) / config.height - LARGE_STAR_CENTER_Y_RATIO) < 0.001,
            "半径比例": abs(large_star.radius / config.height - LARGE_STAR_RADIUS_RATIO) < 0.001,
            "旋转角度": large_star.rotation == 0.0,
            "颜色标准": large_star.color == STAR_YELLOW
        }
        
        for check_name, result in checks.items():
            self.assertTrue(result, msg=f"标准检查失败: {check_name}")
        
        print("✓ 大星国家标准符合性验证通过")


if __name__ == "__main__":
    # 运行测试
    print("=" * 50)
    print("大五角星绘制功能单元测试")
    print("=" * 50)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestLargeStarDrawing))
    suite.addTests(loader.loadTestsFromTestCase(TestLargeStarIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✓ 所有测试通过！大五角星绘制功能符合要求")
    else:
        print("✗ 部分测试失败，请检查实现")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"原因: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"原因: {error[1]}")
    print("=" * 50)