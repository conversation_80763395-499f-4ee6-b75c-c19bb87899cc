"""
几何计算器单元测试
验证所有几何计算功能的准确性和正确性
"""

import unittest
import math
from typing import List, Tuple

from geometry import GeometryCalculator, Point, create_star_points, point_to_point_angle
from constants import STAR_INNER_RADIUS_RATIO


class TestGeometryCalculator(unittest.TestCase):
    """几何计算器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.calc = GeometryCalculator()
        self.tolerance = 1e-10  # 浮点数比较容差
    
    def test_calculate_star_points_basic(self):
        """测试基本五角星顶点计算"""
        center = (0, 0)
        radius = 100
        
        points = self.calc.calculate_star_points(center, radius)
        
        # 验证顶点数量
        self.assertEqual(len(points), 10, "五角星应该有10个顶点")
        
        # 验证几何形状
        self.assertTrue(
            self.calc.validate_star_geometry(center, radius, points),
            "五角星几何形状验证失败"
        )
    
    def test_calculate_star_points_with_rotation(self):
        """测试带旋转的五角星顶点计算"""
        center = (50, 50)
        radius = 80
        rotation = math.pi / 4  # 45度旋转
        
        points = self.calc.calculate_star_points(center, radius, rotation)
        
        # 验证顶点数量
        self.assertEqual(len(points), 10)
        
        # 验证第一个顶点的位置（应该旋转了45度）
        first_point = Point(*points[0])
        center_point = Point(*center)
        
        # 计算第一个顶点相对于中心的角度
        dx = first_point.x - center_point.x
        dy = first_point.y - center_point.y
        actual_angle = math.atan2(dx, dy)
        
        # 应该接近45度（π/4弧度）
        self.assertAlmostEqual(actual_angle, rotation, places=6)
    
    def test_calculate_star_points_invalid_radius(self):
        """测试无效半径的错误处理"""
        center = (0, 0)
        
        with self.assertRaises(ValueError):
            self.calc.calculate_star_points(center, 0)
        
        with self.assertRaises(ValueError):
            self.calc.calculate_star_points(center, -10)
    
    def test_calculate_pointing_angle(self):
        """测试角度计算功能"""
        # 测试基本方向
        test_cases = [
            ((0, 0), (0, 100), 0),           # 正北方向
            ((0, 0), (100, 0), math.pi/2),   # 正东方向
            ((0, 0), (0, -100), math.pi),    # 正南方向
            ((0, 0), (-100, 0), -math.pi/2), # 正西方向
        ]
        
        for from_point, to_point, expected_angle in test_cases:
            with self.subTest(from_point=from_point, to_point=to_point):
                angle = self.calc.calculate_pointing_angle(from_point, to_point)
                self.assertAlmostEqual(angle, expected_angle, places=6)
    
    def test_calculate_pointing_angle_diagonal(self):
        """测试对角线方向的角度计算"""
        # 东北方向（45度）
        angle = self.calc.calculate_pointing_angle((0, 0), (100, 100))
        expected = math.pi / 4
        self.assertAlmostEqual(angle, expected, places=6)
        
        # 西南方向（225度）
        angle = self.calc.calculate_pointing_angle((0, 0), (-100, -100))
        expected = -3 * math.pi / 4
        self.assertAlmostEqual(angle, expected, places=6)
    
    def test_calculate_pointing_angle_same_point(self):
        """测试相同点的错误处理"""
        with self.assertRaises(ValueError):
            self.calc.calculate_pointing_angle((0, 0), (0, 0))
    
    def test_convert_relative_to_absolute(self):
        """测试相对坐标转绝对坐标转换"""
        flag_size = (600, 400)
        flag_top_left = (-300, 200)
        
        test_cases = [
            ((0, 0), (-300, 200)),      # 左上角
            ((1, 0), (300, 200)),       # 右上角
            ((0, 1), (-300, -200)),     # 左下角
            ((1, 1), (300, -200)),      # 右下角
            ((0.5, 0.5), (0, 0)),       # 中心点
            ((0.25, 0.25), (-150, 100)), # 1/4位置
        ]
        
        for relative_pos, expected_abs in test_cases:
            with self.subTest(relative_pos=relative_pos):
                result = self.calc.convert_relative_to_absolute(
                    relative_pos, flag_size, flag_top_left
                )
                self.assertAlmostEqual(result[0], expected_abs[0], places=6)
                self.assertAlmostEqual(result[1], expected_abs[1], places=6)
    
    def test_convert_relative_to_absolute_invalid_range(self):
        """测试无效相对位置的错误处理"""
        flag_size = (600, 400)
        flag_top_left = (-300, 200)
        
        invalid_positions = [
            (-0.1, 0.5),  # x < 0
            (1.1, 0.5),   # x > 1
            (0.5, -0.1),  # y < 0
            (0.5, 1.1),   # y > 1
        ]
        
        for invalid_pos in invalid_positions:
            with self.subTest(invalid_pos=invalid_pos):
                with self.assertRaises(ValueError):
                    self.calc.convert_relative_to_absolute(
                        invalid_pos, flag_size, flag_top_left
                    )
    
    def test_calculate_star_rotation_for_pointing(self):
        """测试星星指向目标的旋转角度计算"""
        star_center = (0, 0)
        target_center = (100, 0)  # 正东方向
        
        rotation = self.calc.calculate_star_rotation_for_pointing(
            star_center, target_center
        )
        
        # 应该是π/2弧度（90度）
        expected = math.pi / 2
        self.assertAlmostEqual(rotation, expected, places=6)
    
    def test_validate_star_geometry_correct(self):
        """测试正确五角星几何验证"""
        center = (0, 0)
        radius = 100
        points = self.calc.calculate_star_points(center, radius)
        
        result = self.calc.validate_star_geometry(center, radius, points)
        self.assertTrue(result, "正确的五角星几何应该通过验证")
    
    def test_validate_star_geometry_incorrect(self):
        """测试错误五角星几何验证"""
        center = (0, 0)
        radius = 100
        
        # 错误的顶点数量
        wrong_points = [(0, 0)] * 8  # 只有8个点
        result = self.calc.validate_star_geometry(center, radius, wrong_points)
        self.assertFalse(result, "错误的顶点数量应该验证失败")
        
        # 错误的顶点位置
        wrong_points = [(i, i) for i in range(10)]  # 随意的点
        result = self.calc.validate_star_geometry(center, radius, wrong_points)
        self.assertFalse(result, "错误的顶点位置应该验证失败")
    
    def test_get_star_bounding_box(self):
        """测试五角星边界框计算"""
        center = (0, 0)
        radius = 100
        points = self.calc.calculate_star_points(center, radius)
        
        min_x, min_y, max_x, max_y = self.calc.get_star_bounding_box(points)
        
        # 边界框应该大致在 [-radius, -radius, radius, radius] 范围内
        self.assertLessEqual(min_x, -radius * 0.5)  # 至少一半半径
        self.assertGreaterEqual(max_x, radius * 0.5)
        self.assertLessEqual(min_y, -radius * 0.5)
        self.assertGreaterEqual(max_y, radius * 0.5)
    
    def test_get_star_bounding_box_empty_points(self):
        """测试空顶点列表的错误处理"""
        with self.assertRaises(ValueError):
            self.calc.get_star_bounding_box([])
    
    def test_normalize_angle(self):
        """测试角度标准化"""
        test_cases = [
            (0, 0),
            (math.pi, math.pi),
            (2 * math.pi, 0),
            (3 * math.pi, math.pi),
            (-math.pi, math.pi),
            (-2 * math.pi, 0),
        ]
        
        for input_angle, expected in test_cases:
            with self.subTest(input_angle=input_angle):
                result = self.calc.normalize_angle(input_angle)
                self.assertAlmostEqual(result, expected, places=6)
                self.assertGreaterEqual(result, 0)
                self.assertLess(result, 2 * math.pi)
    
    def test_angle_conversion(self):
        """测试角度和弧度转换"""
        # 度转弧度
        self.assertAlmostEqual(
            self.calc.degrees_to_radians(180), math.pi, places=6
        )
        self.assertAlmostEqual(
            self.calc.degrees_to_radians(90), math.pi/2, places=6
        )
        
        # 弧度转度
        self.assertAlmostEqual(
            self.calc.radians_to_degrees(math.pi), 180, places=6
        )
        self.assertAlmostEqual(
            self.calc.radians_to_degrees(math.pi/2), 90, places=6
        )
    
    def test_star_inner_outer_radius_ratio(self):
        """测试五角星内外半径比例的准确性"""
        center = (0, 0)
        radius = 100
        points = self.calc.calculate_star_points(center, radius)
        
        center_point = Point(*center)
        
        # 检查外顶点距离
        for i in range(0, 10, 2):  # 外顶点
            point = Point(*points[i])
            distance = center_point.distance_to(point)
            self.assertAlmostEqual(distance, radius, places=6)
        
        # 检查内顶点距离
        expected_inner_radius = radius * STAR_INNER_RADIUS_RATIO
        for i in range(1, 10, 2):  # 内顶点
            point = Point(*points[i])
            distance = center_point.distance_to(point)
            self.assertAlmostEqual(distance, expected_inner_radius, places=6)


class TestConvenienceFunctions(unittest.TestCase):
    """测试便利函数"""
    
    def test_create_star_points(self):
        """测试创建五角星顶点的便利函数"""
        points = create_star_points(0, 0, 100)
        self.assertEqual(len(points), 10)
        
        # 验证与主函数结果一致
        calc = GeometryCalculator()
        expected_points = calc.calculate_star_points((0, 0), 100)
        
        for actual, expected in zip(points, expected_points):
            self.assertAlmostEqual(actual[0], expected[0], places=6)
            self.assertAlmostEqual(actual[1], expected[1], places=6)
    
    def test_point_to_point_angle(self):
        """测试两点间角度计算的便利函数"""
        angle = point_to_point_angle(0, 0, 0, 100)
        self.assertAlmostEqual(angle, 0, places=6)  # 正北方向


class TestPoint(unittest.TestCase):
    """测试Point类"""
    
    def test_point_creation(self):
        """测试点的创建"""
        p = Point(3, 4)
        self.assertEqual(p.x, 3)
        self.assertEqual(p.y, 4)
    
    def test_point_unpacking(self):
        """测试点的元组解包"""
        p = Point(3, 4)
        x, y = p
        self.assertEqual(x, 3)
        self.assertEqual(y, 4)
    
    def test_distance_calculation(self):
        """测试距离计算"""
        p1 = Point(0, 0)
        p2 = Point(3, 4)
        
        distance = p1.distance_to(p2)
        expected = 5.0  # 3-4-5直角三角形
        
        self.assertAlmostEqual(distance, expected, places=6)


if __name__ == "__main__":
    # 运行所有测试
    unittest.main(verbosity=2)